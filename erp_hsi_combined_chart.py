#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ERP与恒生指数组合图表
将ERP和恒生指数绘制在同一张图上，分析它们的关系
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import os
from datetime import datetime
import logging

# 设置中文字体
try:
    plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei']
    plt.rcParams['axes.unicode_minus'] = False
except:
    pass

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ERPHSICombinedChart:
    def __init__(self):
        """初始化组合图表分析器"""
        self.data_dir = "data_files"
        self.image_dir = "images"
        os.makedirs(self.image_dir, exist_ok=True)
    
    def load_erp_data(self, years=5):
        """加载ERP数据，限制到最近N年"""
        try:
            erp_file = os.path.join(self.data_dir, "hsi_erp_data.csv")
            if not os.path.exists(erp_file):
                logger.error(f"ERP数据文件不存在: {erp_file}")
                return None

            data = pd.read_csv(erp_file)
            data['日期'] = pd.to_datetime(data['日期'])
            data = data.sort_values('日期').reset_index(drop=True)

            # 限制到最近N年
            cutoff_date = datetime.now() - pd.DateOffset(years=years)
            data = data[data['日期'] >= cutoff_date].copy()
            data = data.reset_index(drop=True)

            logger.info(f"成功加载最近{years}年ERP数据: {len(data)} 条记录")
            logger.info(f"日期范围: {data['日期'].min()} 到 {data['日期'].max()}")

            return data

        except Exception as e:
            logger.error(f"加载ERP数据时出错: {str(e)}")
            return None
    
    def create_combined_charts(self, data):
        """创建ERP与恒生指数的组合图表"""
        try:
            logger.info("创建ERP与恒生指数组合图表...")
            
            # 创建多子图布局
            fig = plt.figure(figsize=(16, 12))
            
            # 1. 主图：ERP和恒生指数双轴图
            ax1 = plt.subplot(2, 2, (1, 2))  # 占据上半部分
            
            # 左轴：恒生指数
            color1 = 'tab:blue'
            ax1.set_xlabel('日期', fontsize=12)
            ax1.set_ylabel('恒生指数', color=color1, fontsize=12)
            line1 = ax1.plot(data['日期'], data['收盘价'], color=color1, linewidth=1.5, label='恒生指数')
            ax1.tick_params(axis='y', labelcolor=color1)
            ax1.grid(True, alpha=0.3)
            
            # 右轴：ERP
            ax2 = ax1.twinx()
            color2 = 'tab:red'
            ax2.set_ylabel('ERP (%)', color=color2, fontsize=12)
            line2 = ax2.plot(data['日期'], data['ERP_百分比'], color=color2, linewidth=2, label='ERP')
            ax2.tick_params(axis='y', labelcolor=color2)
            
            # 添加ERP均值线
            erp_mean = data['ERP_百分比'].mean()
            ax2.axhline(y=erp_mean, color=color2, linestyle='--', alpha=0.7, label=f'ERP均值 ({erp_mean:.2f}%)')
            
            # 设置标题和图例
            ax1.set_title('恒生指数与ERP走势对比 - 最近5年', fontsize=14, fontweight='bold', pad=20)
            
            # 合并图例
            lines1, labels1 = ax1.get_legend_handles_labels()
            lines2, labels2 = ax2.get_legend_handles_labels()
            ax1.legend(lines1 + lines2, labels1 + labels2, loc='upper left')
            
            # 2. 左下图：ERP分布直方图
            ax3 = plt.subplot(2, 2, 3)
            
            ax3.hist(data['ERP_百分比'], bins=50, alpha=0.7, color='skyblue', edgecolor='black')
            ax3.axvline(x=erp_mean, color='red', linestyle='--', linewidth=2, label=f'均值 ({erp_mean:.2f}%)')
            
            # 标注当前ERP
            current_erp = data['ERP_百分比'].iloc[-1]
            ax3.axvline(x=current_erp, color='orange', linestyle='-', linewidth=2, label=f'当前 ({current_erp:.2f}%)')
            
            ax3.set_title('ERP分布直方图 - 最近5年')
            ax3.set_xlabel('ERP (%)')
            ax3.set_ylabel('频次')
            ax3.legend()
            ax3.grid(True, alpha=0.3)
            
            # 3. 右下图：ERP vs 恒生指数散点图
            ax4 = plt.subplot(2, 2, 4)
            
            # 使用颜色表示时间（越新越红）
            colors = plt.cm.viridis(np.linspace(0, 1, len(data)))
            scatter = ax4.scatter(data['ERP_百分比'], data['收盘价'], c=colors, alpha=0.6, s=10)
            
            # 添加趋势线
            z = np.polyfit(data['ERP_百分比'], data['收盘价'], 1)
            p = np.poly1d(z)
            ax4.plot(data['ERP_百分比'], p(data['ERP_百分比']), "r--", alpha=0.8, linewidth=2)
            
            # 计算相关系数
            correlation = data['ERP_百分比'].corr(data['收盘价'])
            
            ax4.set_title(f'ERP vs 恒生指数散点图 - 最近5年\n相关系数: {correlation:.3f}')
            ax4.set_xlabel('ERP (%)')
            ax4.set_ylabel('恒生指数')
            ax4.grid(True, alpha=0.3)
            
            # 添加颜色条
            cbar = plt.colorbar(scatter, ax=ax4)
            cbar.set_label('时间 (越新越亮)', rotation=270, labelpad=15)
            
            plt.tight_layout()
            
            # 保存图表
            image_path = os.path.join(self.image_dir, 'erp_hsi_combined_analysis_5years.png')
            plt.savefig(image_path, dpi=300, bbox_inches='tight')
            logger.info(f"组合图表已保存到: {image_path}")
            
            plt.close()
            
            return correlation
            
        except Exception as e:
            logger.error(f"创建组合图表时出错: {str(e)}")
            return None
    
    def create_time_series_analysis(self, data):
        """创建时间序列分析图表"""
        try:
            logger.info("创建时间序列分析图表...")
            
            # 计算滚动相关性
            window = 252  # 1年窗口
            data['滚动相关性'] = data['ERP_百分比'].rolling(window).corr(data['收盘价'])
            
            # 计算ERP和恒生指数的标准化值（便于比较）
            data['ERP_标准化'] = (data['ERP_百分比'] - data['ERP_百分比'].mean()) / data['ERP_百分比'].std()
            data['HSI_标准化'] = (data['收盘价'] - data['收盘价'].mean()) / data['收盘价'].std()
            
            fig, axes = plt.subplots(3, 1, figsize=(16, 12))
            fig.suptitle('ERP与恒生指数时间序列分析 - 最近5年', fontsize=16, fontweight='bold')
            
            # 1. 标准化后的对比
            ax1 = axes[0]
            ax1.plot(data['日期'], data['ERP_标准化'], 'r-', linewidth=1.5, label='ERP (标准化)', alpha=0.8)
            ax1.plot(data['日期'], data['HSI_标准化'], 'b-', linewidth=1.5, label='恒生指数 (标准化)', alpha=0.8)
            ax1.axhline(y=0, color='black', linestyle='-', alpha=0.3)
            ax1.set_title('标准化后的ERP与恒生指数对比 - 最近5年')
            ax1.set_ylabel('标准化值')
            ax1.legend()
            ax1.grid(True, alpha=0.3)
            
            # 2. 滚动相关性
            ax2 = axes[1]
            ax2.plot(data['日期'], data['滚动相关性'], 'g-', linewidth=2, label='1年滚动相关性')
            ax2.axhline(y=0, color='black', linestyle='-', alpha=0.3)
            ax2.axhline(y=data['滚动相关性'].mean(), color='red', linestyle='--', alpha=0.7, 
                       label=f'平均相关性 ({data["滚动相关性"].mean():.3f})')
            ax2.set_title('ERP与恒生指数的滚动相关性 - 最近5年')
            ax2.set_ylabel('相关系数')
            ax2.legend()
            ax2.grid(True, alpha=0.3)
            
            # 3. ERP分位数与未来收益率关系
            ax3 = axes[2]
            
            # 计算未来3个月收益率
            data['未来3月收益率'] = data['收盘价'].pct_change(periods=63).shift(-63) * 100
            
            # 按ERP分位数分组
            data['ERP_分位数'] = pd.qcut(data['ERP_百分比'], q=10, labels=False) + 1
            
            # 计算各分位数的平均未来收益率
            erp_return_analysis = data.groupby('ERP_分位数')['未来3月收益率'].mean()
            
            bars = ax3.bar(erp_return_analysis.index, erp_return_analysis.values, alpha=0.7, color='purple')
            ax3.axhline(y=0, color='black', linestyle='-', alpha=0.3)
            ax3.set_title('ERP分位数与未来3个月收益率关系 - 最近5年')
            ax3.set_xlabel('ERP分位数 (1=最低, 10=最高)')
            ax3.set_ylabel('平均未来3个月收益率 (%)')
            ax3.grid(True, alpha=0.3)
            
            # 添加数值标签
            for bar in bars:
                height = bar.get_height()
                if not pd.isna(height):
                    ax3.text(bar.get_x() + bar.get_width()/2., height,
                            f'{height:.1f}%', ha='center',
                            va='bottom' if height >= 0 else 'top')
            
            plt.tight_layout()
            
            # 保存图表
            image_path = os.path.join(self.image_dir, 'erp_hsi_time_series_analysis_5years.png')
            plt.savefig(image_path, dpi=300, bbox_inches='tight')
            logger.info(f"时间序列分析图表已保存到: {image_path}")
            
            plt.close()
            
            return erp_return_analysis
            
        except Exception as e:
            logger.error(f"创建时间序列分析图表时出错: {str(e)}")
            return None
    
    def analyze_erp_hsi_relationship(self, data):
        """分析ERP与恒生指数的关系"""
        try:
            logger.info("分析ERP与恒生指数的关系...")
            
            # 基本统计
            correlation = data['ERP_百分比'].corr(data['收盘价'])
            
            # 计算不同时期的相关性（基于5年数据）
            periods = {
                '全期间(5年)': data,
                '最近3年': data.tail(252*3),
                '最近2年': data.tail(252*2),
                '最近1年': data.tail(252)
            }
            
            correlations = {}
            for period_name, period_data in periods.items():
                if len(period_data) > 50:  # 确保有足够数据
                    corr = period_data['ERP_百分比'].corr(period_data['收盘价'])
                    correlations[period_name] = corr
            
            # 分析ERP极值时的恒生指数表现
            erp_high = data[data['ERP_百分比'] > data['ERP_百分比'].quantile(0.9)]  # ERP前10%
            erp_low = data[data['ERP_百分比'] < data['ERP_百分比'].quantile(0.1)]   # ERP后10%
            
            # 计算未来收益率（避免重复计算）
            if '未来1月收益率' not in data.columns:
                data['未来1月收益率'] = data['收盘价'].pct_change(periods=21).shift(-21) * 100
            if '未来3月收益率' not in data.columns:
                data['未来3月收益率'] = data['收盘价'].pct_change(periods=63).shift(-63) * 100
            if '未来6月收益率' not in data.columns:
                data['未来6月收益率'] = data['收盘价'].pct_change(periods=126).shift(-126) * 100
            
            high_erp_future_returns = {
                '1个月': erp_high['未来1月收益率'].mean(),
                '3个月': erp_high['未来3月收益率'].mean(),
                '6个月': erp_high['未来6月收益率'].mean()
            }
            
            low_erp_future_returns = {
                '1个月': erp_low['未来1月收益率'].mean(),
                '3个月': erp_low['未来3月收益率'].mean(),
                '6个月': erp_low['未来6月收益率'].mean()
            }
            
            return {
                'overall_correlation': correlation,
                'period_correlations': correlations,
                'high_erp_returns': high_erp_future_returns,
                'low_erp_returns': low_erp_future_returns,
                'erp_stats': {
                    'mean': data['ERP_百分比'].mean(),
                    'std': data['ERP_百分比'].std(),
                    'min': data['ERP_百分比'].min(),
                    'max': data['ERP_百分比'].max(),
                    'current': data['ERP_百分比'].iloc[-1]
                },
                'hsi_stats': {
                    'mean': data['收盘价'].mean(),
                    'std': data['收盘价'].std(),
                    'min': data['收盘价'].min(),
                    'max': data['收盘价'].max(),
                    'current': data['收盘价'].iloc[-1]
                }
            }
            
        except Exception as e:
            logger.error(f"分析ERP与恒生指数关系时出错: {str(e)}")
            return None

def main():
    """主函数"""
    print("=" * 60)
    print("ERP与恒生指数组合分析 - 最近5年")
    print("=" * 60)
    
    # 创建分析器
    analyzer = ERPHSICombinedChart()
    
    # 加载最近5年数据
    data = analyzer.load_erp_data(years=5)
    if data is None:
        print("\n❌ 数据加载失败")
        return
    
    print(f"\n✅ 数据加载成功！")
    print(f"分析期间: {data['日期'].min().strftime('%Y-%m-%d')} 到 {data['日期'].max().strftime('%Y-%m-%d')}")
    print(f"数据点数: {len(data)} 个")
    
    # 创建组合图表
    correlation = analyzer.create_combined_charts(data)
    
    # 创建时间序列分析
    erp_return_analysis = analyzer.create_time_series_analysis(data)
    
    # 分析关系
    analysis_results = analyzer.analyze_erp_hsi_relationship(data)
    
    if analysis_results:
        print(f"\n📊 ERP与恒生指数关系分析:")
        print("=" * 50)
        
        print(f"整体相关系数: {analysis_results['overall_correlation']:.3f}")
        
        print(f"\n不同时期相关性:")
        for period, corr in analysis_results['period_correlations'].items():
            print(f"  {period}: {corr:.3f}")
        
        print(f"\n📈 ERP统计信息:")
        erp_stats = analysis_results['erp_stats']
        print(f"  当前值: {erp_stats['current']:.2f}%")
        print(f"  历史均值: {erp_stats['mean']:.2f}%")
        print(f"  标准差: {erp_stats['std']:.2f}%")
        print(f"  范围: {erp_stats['min']:.2f}% - {erp_stats['max']:.2f}%")
        
        print(f"\n📈 恒生指数统计信息:")
        hsi_stats = analysis_results['hsi_stats']
        print(f"  当前值: {hsi_stats['current']:.0f}")
        print(f"  历史均值: {hsi_stats['mean']:.0f}")
        print(f"  标准差: {hsi_stats['std']:.0f}")
        print(f"  范围: {hsi_stats['min']:.0f} - {hsi_stats['max']:.0f}")
        
        print(f"\n🔍 ERP极值时的未来收益率:")
        print(f"高ERP时期（前10%）的平均未来收益率:")
        for period, return_rate in analysis_results['high_erp_returns'].items():
            if not pd.isna(return_rate):
                print(f"  {period}: {return_rate:+.2f}%")

        print(f"\n低ERP时期（后10%）的平均未来收益率:")
        for period, return_rate in analysis_results['low_erp_returns'].items():
            if not pd.isna(return_rate):
                print(f"  {period}: {return_rate:+.2f}%")
    
    if erp_return_analysis is not None:
        print(f"\n📊 ERP分位数与未来3个月收益率:")
        print("-" * 40)
        for decile, avg_return in erp_return_analysis.items():
            if not pd.isna(avg_return):
                print(f"第{decile:2d}分位: {avg_return:+7.2f}%")
    
    # 当前市场状态
    latest = data.iloc[-1]
    erp_percentile = (data['ERP_百分比'] <= latest['ERP_百分比']).mean() * 100
    
    print(f"\n🎯 当前市场状态 ({latest['日期'].strftime('%Y-%m-%d')}):")
    print("-" * 50)
    print(f"恒生指数: {latest['收盘价']:.0f}")
    print(f"ERP: {latest['ERP_百分比']:.2f}%")
    print(f"ERP历史百分位: {erp_percentile:.1f}%")
    
    if erp_percentile > 80:
        print("💡 ERP处于历史高位，股票相对债券具有较高吸引力")
    elif erp_percentile < 20:
        print("💡 ERP处于历史低位，债券相对股票具有较高吸引力")
    else:
        print("💡 ERP处于中等水平，股债配置相对均衡")
    
    print(f"\n✅ 分析完成！图表已保存到 images 目录")

if __name__ == "__main__":
    main()
