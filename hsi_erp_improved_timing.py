#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进的ERP择时策略分析
基于ERP的恒生指数择时策略优化版本
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import os
from datetime import datetime
import logging

# 设置中文字体
try:
    plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei']
    plt.rcParams['axes.unicode_minus'] = False
except:
    pass

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ImprovedERPTiming:
    def __init__(self):
        """初始化改进的择时策略"""
        self.data_dir = "data_files"
        self.image_dir = "images"
        os.makedirs(self.image_dir, exist_ok=True)
    
    def load_erp_data(self):
        """加载ERP数据"""
        try:
            erp_file = os.path.join(self.data_dir, "hsi_erp_data.csv")
            data = pd.read_csv(erp_file)
            data['日期'] = pd.to_datetime(data['日期'])
            data = data.sort_values('日期').reset_index(drop=True)
            return data
        except Exception as e:
            logger.error(f"加载数据出错: {e}")
            return None
    
    def analyze_erp_timing_effectiveness(self, data):
        """分析ERP择时的有效性"""
        try:
            logger.info("分析ERP择时有效性...")
            
            # 计算前瞻收益率（未来1个月、3个月、6个月、1年）
            periods = [21, 63, 126, 252]  # 交易日
            period_names = ['1个月', '3个月', '6个月', '1年']
            
            for i, period in enumerate(periods):
                period_name = period_names[i]
                data[f'未来{period_name}收益率'] = data['收盘价'].pct_change(periods=period).shift(-period) * 100
            
            # 按ERP分位数分组分析
            data['ERP_分位数'] = pd.qcut(data['ERP_百分比'], q=10, labels=False) + 1
            
            # 计算各分位数的平均未来收益率
            erp_analysis = data.groupby('ERP_分位数').agg({
                'ERP_百分比': 'mean',
                '未来1个月收益率': 'mean',
                '未来3个月收益率': 'mean', 
                '未来6个月收益率': 'mean',
                '未来1年收益率': 'mean'
            }).round(2)
            
            print("\n" + "=" * 80)
            print("ERP分位数与未来收益率关系分析")
            print("=" * 80)
            print("ERP分位数 | 平均ERP | 未来1月 | 未来3月 | 未来6月 | 未来1年")
            print("-" * 80)
            
            for decile in range(1, 11):
                if decile in erp_analysis.index:
                    row = erp_analysis.loc[decile]
                    print(f"第{decile:2d}分位 | {row['ERP_百分比']:7.2f}% | {row['未来1个月收益率']:7.2f}% | {row['未来3个月收益率']:7.2f}% | {row['未来6个月收益率']:7.2f}% | {row['未来1年收益率']:7.2f}%")
            
            # 计算相关性
            correlations = {}
            for period_name in period_names:
                corr = data['ERP_百分比'].corr(data[f'未来{period_name}收益率'])
                correlations[period_name] = corr
            
            print(f"\n📊 ERP与未来收益率相关性:")
            for period, corr in correlations.items():
                print(f"  {period}: {corr:.3f}")
            
            return data, erp_analysis, correlations
            
        except Exception as e:
            logger.error(f"分析ERP择时有效性时出错: {e}")
            return None, None, None
    
    def create_improved_strategies(self, data):
        """创建改进的择时策略"""
        try:
            logger.info("创建改进的择时策略...")
            
            # 策略1: 极值策略（只在极端情况下交易）
            erp_95th = data['ERP_百分比'].quantile(0.95)
            erp_5th = data['ERP_百分比'].quantile(0.05)
            
            data['极值_买入信号'] = data['ERP_百分比'] > erp_95th
            data['极值_卖出信号'] = data['ERP_百分比'] < erp_5th
            
            # 策略2: 趋势确认策略（ERP连续上升/下降）
            data['ERP_MA5'] = data['ERP_百分比'].rolling(5).mean()
            data['ERP_MA20'] = data['ERP_百分比'].rolling(20).mean()
            
            data['趋势_买入信号'] = (data['ERP_百分比'] > data['ERP_MA5']) & (data['ERP_MA5'] > data['ERP_MA20']) & (data['ERP_百分比'] > 6.0)
            data['趋势_卖出信号'] = (data['ERP_百分比'] < data['ERP_MA5']) & (data['ERP_MA5'] < data['ERP_MA20']) & (data['ERP_百分比'] < 3.0)
            
            # 策略3: 均值回归策略（ERP偏离均值时反向操作）
            data['ERP_长期均值'] = data['ERP_百分比'].rolling(252).mean()
            data['ERP_偏离度'] = (data['ERP_百分比'] - data['ERP_长期均值']) / data['ERP_长期均值']
            
            data['均值回归_买入信号'] = data['ERP_偏离度'] < -0.3  # ERP比长期均值低30%以上
            data['均值回归_卖出信号'] = data['ERP_偏离度'] > 0.3   # ERP比长期均值高30%以上
            
            # 策略4: 组合策略（多条件确认）
            data['组合_买入信号'] = (
                (data['ERP_百分比'] > 6.0) &  # ERP绝对值高
                (data['ERP_百分比'] > data['ERP_百分比'].rolling(20).quantile(0.8)) &  # 相对高位
                (data['收盘价'] < data['收盘价'].rolling(252).mean())  # 价格相对低位
            )
            
            data['组合_卖出信号'] = (
                (data['ERP_百分比'] < 3.0) |  # ERP绝对值低
                (data['ERP_百分比'] < data['ERP_百分比'].rolling(20).quantile(0.2))  # 相对低位
            )
            
            return data
            
        except Exception as e:
            logger.error(f"创建改进策略时出错: {e}")
            return None
    
    def backtest_improved_strategy(self, data, strategy_name, min_hold_days=21):
        """回测改进策略（增加最小持有期）"""
        try:
            buy_signal_col = f"{strategy_name}_买入信号"
            sell_signal_col = f"{strategy_name}_卖出信号"
            
            positions = []
            returns = []
            trades = []
            
            current_position = 0
            entry_price = 0
            entry_date = None
            
            for i, row in data.iterrows():
                date = row['日期']
                price = row['收盘价']
                buy_signal = row[buy_signal_col]
                sell_signal = row[sell_signal_col]
                
                # 交易逻辑（增加最小持有期限制）
                if current_position == 0 and buy_signal:
                    current_position = 1
                    entry_price = price
                    entry_date = date
                    trades.append({
                        '日期': date,
                        '操作': '买入',
                        '价格': price,
                        'ERP': row['ERP_百分比']
                    })
                elif current_position == 1 and sell_signal:
                    # 检查是否满足最小持有期
                    if entry_date is None or (date - entry_date).days >= min_hold_days:
                        current_position = 0
                        exit_return = (price - entry_price) / entry_price
                        trades.append({
                            '日期': date,
                            '操作': '卖出',
                            '价格': price,
                            'ERP': row['ERP_百分比'],
                            '收益率': exit_return * 100,
                            '持有天数': (date - entry_date).days if entry_date else 0
                        })
                
                positions.append(current_position)
                
                # 计算当日收益率
                if i == 0:
                    daily_return = 0
                else:
                    if current_position == 1:
                        daily_return = (price - data.iloc[i-1]['收盘价']) / data.iloc[i-1]['收盘价']
                    else:
                        daily_return = 0
                
                returns.append(daily_return)
            
            data[f'{strategy_name}_持仓'] = positions
            data[f'{strategy_name}_收益率'] = returns
            data[f'{strategy_name}_累计收益'] = (1 + pd.Series(returns)).cumprod()
            
            return trades
            
        except Exception as e:
            logger.error(f"回测策略时出错: {e}")
            return []
    
    def analyze_strategy_performance(self, data, strategy_name, trades):
        """分析策略表现"""
        try:
            if not trades:
                return None
            
            # 计算交易统计
            completed_trades = [t for t in trades if '收益率' in t]
            
            if not completed_trades:
                return None
            
            trade_returns = [t['收益率'] for t in completed_trades]
            hold_days = [t['持有天数'] for t in completed_trades]
            
            # 基本统计
            total_trades = len(completed_trades)
            win_trades = len([r for r in trade_returns if r > 0])
            win_rate = win_trades / total_trades if total_trades > 0 else 0
            
            avg_return = np.mean(trade_returns)
            avg_win = np.mean([r for r in trade_returns if r > 0]) if win_trades > 0 else 0
            avg_loss = np.mean([r for r in trade_returns if r < 0]) if (total_trades - win_trades) > 0 else 0
            
            avg_hold_days = np.mean(hold_days)
            
            # 策略整体表现
            strategy_returns = data[f'{strategy_name}_收益率']
            total_return = data[f'{strategy_name}_累计收益'].iloc[-1] - 1
            
            # 年化收益率
            years = (data['日期'].iloc[-1] - data['日期'].iloc[0]).days / 365.25
            annual_return = (1 + total_return) ** (1/years) - 1
            
            # 最大回撤
            cumulative = data[f'{strategy_name}_累计收益']
            rolling_max = cumulative.expanding().max()
            drawdown = (cumulative - rolling_max) / rolling_max
            max_drawdown = drawdown.min()
            
            return {
                '策略名称': strategy_name,
                '总交易次数': total_trades,
                '获胜交易': win_trades,
                '胜率': win_rate * 100,
                '平均收益率': avg_return,
                '平均获胜收益': avg_win,
                '平均亏损': avg_loss,
                '平均持有天数': avg_hold_days,
                '总收益率': total_return * 100,
                '年化收益率': annual_return * 100,
                '最大回撤': max_drawdown * 100
            }
            
        except Exception as e:
            logger.error(f"分析策略表现时出错: {e}")
            return None
    
    def plot_improved_analysis(self, data, all_performance):
        """绘制改进分析图表"""
        try:
            fig, axes = plt.subplots(2, 2, figsize=(16, 12))
            fig.suptitle('改进的ERP择时策略分析', fontsize=16, fontweight='bold')
            
            # 1. ERP与未来收益率关系
            ax1 = axes[0, 0]
            
            # 按ERP分位数分组
            erp_deciles = []
            future_returns = []
            
            for decile in range(1, 11):
                decile_data = data[data['ERP_分位数'] == decile]
                if not decile_data.empty and '未来3个月收益率' in decile_data.columns:
                    erp_deciles.append(decile)
                    future_returns.append(decile_data['未来3个月收益率'].mean())
            
            if erp_deciles and future_returns:
                ax1.bar(erp_deciles, future_returns, alpha=0.7)
                ax1.set_title('ERP分位数 vs 未来3个月收益率')
                ax1.set_xlabel('ERP分位数')
                ax1.set_ylabel('平均未来3个月收益率 (%)')
                ax1.grid(True, alpha=0.3)
            
            # 2. 改进策略累计收益对比
            ax2 = axes[0, 1]
            
            # 买入持有基准
            if '买入持有_累计收益' not in data.columns:
                data['买入持有_收益率'] = data['收盘价'].pct_change().fillna(0)
                data['买入持有_累计收益'] = (1 + data['买入持有_收益率']).cumprod()
            
            ax2.plot(data['日期'], (data['买入持有_累计收益'] - 1) * 100, 
                    'k-', linewidth=2, label='买入持有', alpha=0.8)
            
            # 改进策略
            strategy_names = ['极值', '趋势', '均值回归', '组合']
            colors = ['red', 'blue', 'green', 'orange']
            
            for i, strategy_name in enumerate(strategy_names):
                if f'{strategy_name}_累计收益' in data.columns:
                    ax2.plot(data['日期'], (data[f'{strategy_name}_累计收益'] - 1) * 100,
                            color=colors[i], linewidth=1.5, label=f'{strategy_name}策略')
            
            ax2.set_title('改进策略累计收益率对比')
            ax2.set_xlabel('日期')
            ax2.set_ylabel('累计收益率 (%)')
            ax2.legend()
            ax2.grid(True, alpha=0.3)
            
            # 3. 策略绩效指标对比
            ax3 = axes[1, 0]
            
            if all_performance:
                strategies = list(all_performance.keys())
                annual_returns = [all_performance[s]['年化收益率'] for s in strategies]
                max_drawdowns = [abs(all_performance[s]['最大回撤']) for s in strategies]
                
                x = np.arange(len(strategies))
                width = 0.35
                
                ax3.bar(x - width/2, annual_returns, width, label='年化收益率', alpha=0.8)
                ax3.bar(x + width/2, max_drawdowns, width, label='最大回撤', alpha=0.8)
                
                ax3.set_title('改进策略绩效对比')
                ax3.set_xlabel('策略')
                ax3.set_ylabel('百分比 (%)')
                ax3.set_xticks(x)
                ax3.set_xticklabels(strategies, rotation=45)
                ax3.legend()
                ax3.grid(True, alpha=0.3)
            
            # 4. 胜率和平均持有天数
            ax4 = axes[1, 1]
            
            if all_performance:
                win_rates = [all_performance[s]['胜率'] for s in strategies]
                hold_days = [all_performance[s]['平均持有天数'] for s in strategies]
                
                ax4_twin = ax4.twinx()
                
                bars1 = ax4.bar(x - width/2, win_rates, width, label='胜率', alpha=0.8, color='blue')
                bars2 = ax4_twin.bar(x + width/2, hold_days, width, label='平均持有天数', alpha=0.8, color='red')
                
                ax4.set_title('胜率与平均持有天数')
                ax4.set_xlabel('策略')
                ax4.set_ylabel('胜率 (%)', color='blue')
                ax4_twin.set_ylabel('平均持有天数', color='red')
                ax4.set_xticks(x)
                ax4.set_xticklabels(strategies, rotation=45)
                ax4.grid(True, alpha=0.3)
            
            plt.tight_layout()
            
            # 保存图表
            image_path = os.path.join(self.image_dir, 'hsi_erp_improved_timing.png')
            plt.savefig(image_path, dpi=300, bbox_inches='tight')
            logger.info(f"改进策略分析图表已保存到: {image_path}")
            
            plt.close()
            
        except Exception as e:
            logger.error(f"绘制改进分析图表时出错: {e}")

def main():
    """主函数"""
    print("=" * 60)
    print("改进的ERP择时策略分析")
    print("=" * 60)
    
    analyzer = ImprovedERPTiming()
    
    # 加载数据
    data = analyzer.load_erp_data()
    if data is None:
        return
    
    # 分析ERP择时有效性
    data, erp_analysis, correlations = analyzer.analyze_erp_timing_effectiveness(data)
    if data is None:
        return
    
    # 创建改进策略
    data = analyzer.create_improved_strategies(data)
    if data is None:
        return
    
    print(f"\n✅ 策略创建完成！")
    
    # 回测改进策略
    strategy_names = ['极值', '趋势', '均值回归', '组合']
    all_performance = {}
    
    for strategy_name in strategy_names:
        print(f"\n📊 回测改进策略: {strategy_name}")
        
        trades = analyzer.backtest_improved_strategy(data, strategy_name)
        performance = analyzer.analyze_strategy_performance(data, strategy_name, trades)
        
        if performance:
            all_performance[strategy_name] = performance
            
            print(f"  总交易次数: {performance['总交易次数']}")
            print(f"  胜率: {performance['胜率']:.1f}%")
            print(f"  年化收益率: {performance['年化收益率']:.2f}%")
            print(f"  最大回撤: {performance['最大回撤']:.2f}%")
            print(f"  平均持有天数: {performance['平均持有天数']:.1f}天")
    
    # 显示最佳策略
    if all_performance:
        print("\n" + "=" * 60)
        print("改进策略绩效排名")
        print("=" * 60)
        
        # 计算买入持有基准
        if '买入持有_累计收益' not in data.columns:
            data['买入持有_收益率'] = data['收盘价'].pct_change().fillna(0)
            data['买入持有_累计收益'] = (1 + data['买入持有_收益率']).cumprod()
        
        benchmark_total = (data['买入持有_累计收益'].iloc[-1] - 1) * 100
        years = (data['日期'].iloc[-1] - data['日期'].iloc[0]).days / 365.25
        benchmark_annual = ((1 + benchmark_total/100) ** (1/years) - 1) * 100
        
        print(f"买入持有基准: 年化收益率 {benchmark_annual:.2f}%")
        print()
        
        sorted_strategies = sorted(all_performance.items(), 
                                 key=lambda x: x[1]['年化收益率'], reverse=True)
        
        for i, (name, perf) in enumerate(sorted_strategies, 1):
            excess_return = perf['年化收益率'] - benchmark_annual
            print(f"{i}. {name}策略:")
            print(f"   年化收益率: {perf['年化收益率']:.2f}%")
            print(f"   超额收益: {excess_return:+.2f}%")
            print(f"   胜率: {perf['胜率']:.1f}%")
            print(f"   最大回撤: {perf['最大回撤']:.2f}%")
            print(f"   交易次数: {perf['总交易次数']}")
    
    # 绘制分析图表
    analyzer.plot_improved_analysis(data, all_performance)
    
    print(f"\n✅ 改进择时策略分析完成！")

if __name__ == "__main__":
    main()
