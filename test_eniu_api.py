#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试eniu.com API接口
"""

import requests
import json

def test_eniu_api():
    """测试eniu.com API"""
    print("测试eniu.com API接口")
    print("=" * 50)
    
    url = "https://eniu.com/chart/peindex/hkhsi/t/all"
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Referer': 'https://eniu.com/',
    }
    
    try:
        print(f"请求URL: {url}")
        response = requests.get(url, headers=headers, timeout=30)
        
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        print(f"响应长度: {len(response.text)}")
        
        if response.status_code == 200:
            print("\n响应内容前500字符:")
            print(response.text[:500])
            
            # 尝试解析JSON
            try:
                data = response.json()
                print(f"\nJSON解析成功，数据类型: {type(data)}")
                if isinstance(data, dict):
                    print(f"字典键: {list(data.keys())}")
                elif isinstance(data, list):
                    print(f"列表长度: {len(data)}")
                    if len(data) > 0:
                        print(f"第一个元素: {data[0]}")
            except json.JSONDecodeError:
                print("\nJSON解析失败，可能是HTML页面")
                
                # 检查是否包含数据
                if 'chart' in response.text.lower():
                    print("响应中包含'chart'关键词")
                if 'pe' in response.text.lower():
                    print("响应中包含'pe'关键词")
                if 'data' in response.text.lower():
                    print("响应中包含'data'关键词")
        else:
            print(f"请求失败: {response.status_code}")
            print(f"错误信息: {response.text[:200]}")
            
    except Exception as e:
        print(f"请求出错: {e}")

def test_alternative_urls():
    """测试其他可能的URL"""
    print("\n" + "=" * 50)
    print("测试其他可能的URL")
    print("=" * 50)
    
    urls = [
        "https://eniu.com/api/chart/peindex/hkhsi/t/all",
        "https://eniu.com/chart/peindex/hkhsi",
        "https://eniu.com/api/peindex/hkhsi",
        "https://eniu.com/chart/peindex/hkhsi/data",
    ]
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'application/json, text/plain, */*',
    }
    
    for url in urls:
        try:
            print(f"\n测试URL: {url}")
            response = requests.get(url, headers=headers, timeout=10)
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                print(f"响应长度: {len(response.text)}")
                print(f"前100字符: {response.text[:100]}")
                
                try:
                    data = response.json()
                    print("✅ JSON解析成功")
                except:
                    print("❌ JSON解析失败")
            
        except Exception as e:
            print(f"❌ 请求失败: {e}")

if __name__ == "__main__":
    test_eniu_api()
    test_alternative_urls()
