#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
上证指数3500点以上时间段统计分析
"""

import akshare as ak
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import warnings

warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class ShanghaiIndexAnalyzer:
    def __init__(self):
        self.data = None
        self.above_3500_periods = []
        
    def download_shanghai_index_data(self):
        """下载上证指数历史数据"""
        print("📈 正在下载上证指数历史数据...")
        
        try:
            # 获取上证指数历史数据（从2000年开始到现在）
            end_date = datetime.now().strftime('%Y%m%d')
            start_date = '20000101'  # 从2000年开始
            
            # 使用akshare获取上证指数数据
            data = ak.index_zh_a_hist(
                symbol="000001",  # 上证指数代码
                period="daily",
                start_date=start_date,
                end_date=end_date
            )
            
            if data.empty:
                print("❌ 未能获取到上证指数数据")
                return False
            
            # 数据预处理
            data['日期'] = pd.to_datetime(data['日期'])
            data = data.sort_values('日期').reset_index(drop=True)
            data['收盘'] = pd.to_numeric(data['收盘'], errors='coerce')
            
            # 过滤掉无效数据
            data = data.dropna(subset=['收盘'])
            
            self.data = data
            print(f"✅ 成功获取上证指数数据: {len(data)} 个交易日")
            print(f"📅 数据范围: {data['日期'].min().date()} 到 {data['日期'].max().date()}")
            print(f"📊 收盘价范围: {data['收盘'].min():.2f} - {data['收盘'].max():.2f}")
            
            return True
            
        except Exception as e:
            print(f"❌ 下载上证指数数据失败: {e}")
            return False
    
    def find_above_3500_periods(self):
        """找出上证指数在3500以上的时间段"""
        if self.data is None:
            print("❌ 请先下载数据")
            return
        
        print("\n🔍 分析上证指数3500点以上的时间段...")
        
        # 标记3500以上的交易日
        self.data['above_3500'] = self.data['收盘'] >= 3500
        
        # 找出连续的时间段
        periods = []
        start_date = None
        
        for i, row in self.data.iterrows():
            if row['above_3500'] and start_date is None:
                # 开始一个新的3500以上时间段
                start_date = row['日期']
            elif not row['above_3500'] and start_date is not None:
                # 结束当前时间段
                end_date = self.data.iloc[i-1]['日期']
                periods.append({
                    'start_date': start_date,
                    'end_date': end_date,
                    'duration_days': (end_date - start_date).days + 1
                })
                start_date = None
        
        # 处理最后一个时间段（如果数据结束时仍在3500以上）
        if start_date is not None:
            end_date = self.data.iloc[-1]['日期']
            periods.append({
                'start_date': start_date,
                'end_date': end_date,
                'duration_days': (end_date - start_date).days + 1
            })
        
        self.above_3500_periods = periods
        
        # 计算统计信息
        total_days_above_3500 = sum(period['duration_days'] for period in periods)
        total_trading_days = len(self.data)
        percentage = (total_days_above_3500 / total_trading_days) * 100
        
        print(f"📊 统计结果:")
        print(f"   • 3500点以上时间段数量: {len(periods)} 个")
        print(f"   • 3500点以上总交易日数: {total_days_above_3500} 天")
        print(f"   • 占总交易日比例: {percentage:.2f}%")
        
        return periods
    
    def print_detailed_periods(self):
        """打印详细的时间段信息"""
        if not self.above_3500_periods:
            print("❌ 请先分析时间段")
            return
        
        print(f"\n📋 上证指数3500点以上的详细时间段:")
        print("=" * 80)
        
        for i, period in enumerate(self.above_3500_periods, 1):
            start_str = period['start_date'].strftime('%Y-%m-%d')
            end_str = period['end_date'].strftime('%Y-%m-%d')
            duration = period['duration_days']
            
            # 获取该时间段的最高点和最低点
            period_data = self.data[
                (self.data['日期'] >= period['start_date']) & 
                (self.data['日期'] <= period['end_date'])
            ]
            
            max_price = period_data['收盘'].max()
            min_price = period_data['收盘'].min()
            max_date = period_data[period_data['收盘'] == max_price]['日期'].iloc[0]
            min_date = period_data[period_data['收盘'] == min_price]['日期'].iloc[0]
            
            print(f"第{i:2d}个时间段:")
            print(f"   时间: {start_str} 至 {end_str}")
            print(f"   持续: {duration:4d} 个交易日")
            print(f"   最高: {max_price:7.2f} 点 ({max_date.strftime('%Y-%m-%d')})")
            print(f"   最低: {min_price:7.2f} 点 ({min_date.strftime('%Y-%m-%d')})")
            print(f"   涨幅: {((max_price - min_price) / min_price * 100):6.2f}%")
            print("-" * 60)
    
    def create_visualization(self):
        """创建可视化图表"""
        if self.data is None:
            print("❌ 请先下载数据")
            return
        
        print("\n📊 创建可视化图表...")
        
        # 创建图表
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 12))
        
        # 上图：上证指数走势图，标记3500点以上区域
        ax1.plot(self.data['日期'], self.data['收盘'], linewidth=1, color='blue', alpha=0.7)
        ax1.axhline(y=3500, color='red', linestyle='--', linewidth=2, label='3500点')
        
        # 填充3500以上的区域
        above_3500_mask = self.data['收盘'] >= 3500
        ax1.fill_between(self.data['日期'], 3500, self.data['收盘'], 
                        where=above_3500_mask, alpha=0.3, color='red', label='3500点以上区域')
        
        ax1.set_title('上证指数历史走势及3500点以上时间段', fontsize=16, fontweight='bold')
        ax1.set_ylabel('指数点位', fontsize=12)
        ax1.grid(True, alpha=0.3)
        ax1.legend()
        
        # 下图：时间段分布柱状图
        if self.above_3500_periods:
            periods_df = pd.DataFrame(self.above_3500_periods)
            periods_df['year'] = periods_df['start_date'].dt.year
            
            # 按年份统计
            yearly_stats = periods_df.groupby('year').agg({
                'duration_days': ['count', 'sum']
            }).round(2)
            yearly_stats.columns = ['时间段数量', '总天数']
            
            # 绘制柱状图
            years = yearly_stats.index
            ax2.bar(years, yearly_stats['总天数'], alpha=0.7, color='orange', label='3500点以上天数')
            
            ax2.set_title('各年份上证指数3500点以上交易日统计', fontsize=14, fontweight='bold')
            ax2.set_xlabel('年份', fontsize=12)
            ax2.set_ylabel('交易日数量', fontsize=12)
            ax2.grid(True, alpha=0.3)
            ax2.legend()
        
        plt.tight_layout()
        
        # 保存图表
        filename = f'上证指数3500点以上时间段分析_{datetime.now().strftime("%Y%m%d")}.png'
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        print(f"📊 图表已保存: {filename}")

        # 关闭图表以释放内存
        plt.close()
    
    def save_results_to_csv(self):
        """保存分析结果到CSV文件"""
        if not self.above_3500_periods:
            print("❌ 请先分析时间段")
            return
        
        # 创建详细结果DataFrame
        results = []
        for i, period in enumerate(self.above_3500_periods, 1):
            period_data = self.data[
                (self.data['日期'] >= period['start_date']) & 
                (self.data['日期'] <= period['end_date'])
            ]
            
            max_price = period_data['收盘'].max()
            min_price = period_data['收盘'].min()
            max_date = period_data[period_data['收盘'] == max_price]['日期'].iloc[0]
            min_date = period_data[period_data['收盘'] == min_price]['日期'].iloc[0]
            
            results.append({
                '序号': i,
                '开始日期': period['start_date'].strftime('%Y-%m-%d'),
                '结束日期': period['end_date'].strftime('%Y-%m-%d'),
                '持续天数': period['duration_days'],
                '最高点位': max_price,
                '最高点日期': max_date.strftime('%Y-%m-%d'),
                '最低点位': min_price,
                '最低点日期': min_date.strftime('%Y-%m-%d'),
                '区间涨幅(%)': round((max_price - min_price) / min_price * 100, 2)
            })
        
        results_df = pd.DataFrame(results)
        
        # 保存到CSV
        filename = f'上证指数3500点以上时间段详情_{datetime.now().strftime("%Y%m%d")}.csv'
        results_df.to_csv(filename, index=False, encoding='utf-8-sig')
        print(f"📄 详细结果已保存: {filename}")
        
        return results_df

def main():
    """主函数"""
    print("🎯 上证指数3500点以上时间段统计分析")
    print("=" * 60)
    
    # 创建分析器
    analyzer = ShanghaiIndexAnalyzer()
    
    # 下载数据
    if not analyzer.download_shanghai_index_data():
        return
    
    # 分析3500以上时间段
    periods = analyzer.find_above_3500_periods()
    
    if not periods:
        print("📊 上证指数从未达到3500点以上")
        return
    
    # 打印详细信息
    analyzer.print_detailed_periods()
    
    # 创建可视化
    analyzer.create_visualization()
    
    # 保存结果
    results_df = analyzer.save_results_to_csv()
    
    print(f"\n✅ 分析完成！")
    print(f"📊 共发现 {len(periods)} 个3500点以上的时间段")
    print(f"📄 详细结果已保存到CSV文件")
    print(f"📊 可视化图表已生成")

if __name__ == "__main__":
    main()
