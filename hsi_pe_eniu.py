#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
从eniu.com获取恒生指数PE数据
"""

import requests
import pandas as pd
import json
import time
from datetime import datetime, timedelta
import logging
import os
import matplotlib.pyplot as plt
import numpy as np

# 设置中文字体
try:
    plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
    plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号
except:
    pass

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class HSIPEEniuCollector:
    def __init__(self):
        """初始化恒生指数PE数据收集器"""
        self.data_dir = "data_files"
        self.image_dir = "images"
        os.makedirs(self.data_dir, exist_ok=True)
        os.makedirs(self.image_dir, exist_ok=True)
        
        # 设置请求头
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Referer': 'https://eniu.com/',
        }
        
        # API接口
        self.base_url = "https://eniu.com"
        self.pe_api_url = "https://eniu.com/chart/peindex/hkhsi/t/all"
    
    def get_hsi_pe_data(self):
        """
        从eniu.com获取恒生指数PE数据
        
        Returns:
            pandas.DataFrame: 恒生指数PE数据
        """
        try:
            logger.info("正在从eniu.com获取恒生指数PE数据...")
            
            # 发送请求
            response = requests.get(self.pe_api_url, headers=self.headers, timeout=30)
            
            if response.status_code == 200:
                logger.info("成功获取数据响应")
                
                # 尝试解析JSON数据
                try:
                    data = response.json()
                    logger.info(f"成功解析JSON数据，数据类型: {type(data)}")
                    
                    # 检查数据结构
                    if isinstance(data, dict):
                        logger.info(f"数据字典键: {list(data.keys())}")

                        # eniu.com的数据格式: {"date": [...], "pe": [...], "close": [...]}
                        if 'date' in data and 'pe' in data:
                            return self.parse_eniu_data(data)
                        else:
                            # 查找PE数据
                            pe_data = None
                            if 'data' in data:
                                pe_data = data['data']
                            elif 'result' in data:
                                pe_data = data['result']
                            elif 'chart' in data:
                                pe_data = data['chart']
                            else:
                                # 如果没有明显的数据键，尝试第一个列表类型的值
                                for key, value in data.items():
                                    if isinstance(value, list) and len(value) > 0:
                                        pe_data = value
                                        logger.info(f"使用键 '{key}' 的数据")
                                        break

                            if pe_data:
                                return self.parse_pe_data(pe_data)
                            else:
                                logger.warning("未找到PE数据")
                                return pd.DataFrame()
                    
                    elif isinstance(data, list):
                        logger.info(f"数据是列表，长度: {len(data)}")
                        return self.parse_pe_data(data)
                    
                    else:
                        logger.warning(f"未知的数据格式: {type(data)}")
                        return pd.DataFrame()
                        
                except json.JSONDecodeError as e:
                    logger.error(f"JSON解析失败: {e}")
                    # 尝试从HTML中提取数据
                    return self.parse_html_data(response.text)
                    
            else:
                logger.error(f"请求失败，状态码: {response.status_code}")
                return pd.DataFrame()
                
        except Exception as e:
            logger.error(f"获取恒生指数PE数据时出错: {str(e)}")
            return pd.DataFrame()

    def parse_eniu_data(self, data):
        """
        解析eniu.com的数据格式

        Args:
            data: dict, 包含date、pe、close键的字典

        Returns:
            pandas.DataFrame: 解析后的PE数据
        """
        try:
            logger.info("解析eniu.com数据格式")

            dates = data.get('date', [])
            pe_values = data.get('pe', [])
            close_values = data.get('close', [])

            logger.info(f"日期数量: {len(dates)}")
            logger.info(f"PE数量: {len(pe_values)}")
            logger.info(f"收盘价数量: {len(close_values)}")

            if len(dates) == len(pe_values):
                df_data = []
                for i in range(len(dates)):
                    date_str = dates[i]
                    pe_value = pe_values[i]
                    close_value = close_values[i] if i < len(close_values) else None

                    # 转换日期
                    date = pd.to_datetime(date_str)

                    # 转换PE值
                    pe = float(pe_value) if pe_value is not None and pe_value != '' else None

                    # 转换收盘价
                    close = float(close_value) if close_value is not None and close_value != '' else None

                    df_data.append({
                        '日期': date,
                        'PE': pe,
                        '收盘价': close
                    })

                result_df = pd.DataFrame(df_data)
                result_df = result_df.dropna(subset=['PE'])  # 删除PE为空的行
                result_df = result_df.sort_values('日期').reset_index(drop=True)

                logger.info(f"成功解析 {len(result_df)} 条PE数据")
                logger.info(f"日期范围: {result_df['日期'].min()} 到 {result_df['日期'].max()}")
                logger.info(f"PE范围: {result_df['PE'].min():.2f} 到 {result_df['PE'].max():.2f}")

                return result_df
            else:
                logger.error(f"数据长度不匹配: 日期{len(dates)}, PE{len(pe_values)}")
                return pd.DataFrame()

        except Exception as e:
            logger.error(f"解析eniu.com数据时出错: {str(e)}")
            return pd.DataFrame()

    def parse_pe_data(self, data):
        """
        解析PE数据
        
        Args:
            data: 原始数据
            
        Returns:
            pandas.DataFrame: 解析后的PE数据
        """
        try:
            logger.info(f"开始解析PE数据，数据类型: {type(data)}")
            
            if isinstance(data, list) and len(data) > 0:
                logger.info(f"数据列表长度: {len(data)}")
                logger.info(f"第一个元素: {data[0]}")
                
                # 检查数据格式
                if isinstance(data[0], list):
                    # 数据格式: [[timestamp, pe_value], ...]
                    df_data = []
                    for item in data:
                        if len(item) >= 2:
                            timestamp = item[0]
                            pe_value = item[1]
                            
                            # 转换时间戳
                            if isinstance(timestamp, (int, float)):
                                # 如果是毫秒时间戳，转换为秒
                                if timestamp > 1e10:
                                    timestamp = timestamp / 1000
                                date = datetime.fromtimestamp(timestamp)
                            else:
                                date = pd.to_datetime(timestamp)
                            
                            df_data.append({
                                '日期': date,
                                'PE': float(pe_value) if pe_value is not None else None
                            })
                    
                    if df_data:
                        result_df = pd.DataFrame(df_data)
                        result_df = result_df.dropna()
                        result_df = result_df.sort_values('日期').reset_index(drop=True)
                        
                        logger.info(f"成功解析 {len(result_df)} 条PE数据")
                        return result_df
                
                elif isinstance(data[0], dict):
                    # 数据格式: [{"date": "...", "pe": ...}, ...]
                    df_data = []
                    for item in data:
                        date_key = None
                        pe_key = None
                        
                        # 查找日期和PE字段
                        for key in item.keys():
                            if 'date' in key.lower() or 'time' in key.lower():
                                date_key = key
                            elif 'pe' in key.lower() or 'ratio' in key.lower():
                                pe_key = key
                        
                        if date_key and pe_key:
                            df_data.append({
                                '日期': pd.to_datetime(item[date_key]),
                                'PE': float(item[pe_key]) if item[pe_key] is not None else None
                            })
                    
                    if df_data:
                        result_df = pd.DataFrame(df_data)
                        result_df = result_df.dropna()
                        result_df = result_df.sort_values('日期').reset_index(drop=True)
                        
                        logger.info(f"成功解析 {len(result_df)} 条PE数据")
                        return result_df
            
            logger.warning("无法解析PE数据格式")
            return pd.DataFrame()
            
        except Exception as e:
            logger.error(f"解析PE数据时出错: {str(e)}")
            return pd.DataFrame()
    
    def parse_html_data(self, html_content):
        """
        从HTML内容中提取PE数据
        
        Args:
            html_content: HTML内容
            
        Returns:
            pandas.DataFrame: 提取的PE数据
        """
        try:
            logger.info("尝试从HTML中提取PE数据...")
            
            # 查找可能包含数据的JavaScript变量
            import re
            
            # 查找JSON数据模式
            json_patterns = [
                r'var\s+data\s*=\s*(\[.*?\]);',
                r'data:\s*(\[.*?\])',
                r'chartData\s*=\s*(\[.*?\]);',
                r'peData\s*=\s*(\[.*?\]);',
            ]
            
            for pattern in json_patterns:
                matches = re.findall(pattern, html_content, re.DOTALL)
                for match in matches:
                    try:
                        data = json.loads(match)
                        logger.info(f"从HTML中提取到数据: {len(data)} 条")
                        return self.parse_pe_data(data)
                    except:
                        continue
            
            logger.warning("未能从HTML中提取到PE数据")
            return pd.DataFrame()
            
        except Exception as e:
            logger.error(f"解析HTML数据时出错: {str(e)}")
            return pd.DataFrame()
    
    def save_pe_data(self, data, filename="hsi_pe_eniu.csv"):
        """
        保存PE数据到CSV文件
        
        Args:
            data: pandas.DataFrame, PE数据
            filename: str, 文件名
        """
        if data is None or data.empty:
            logger.warning("数据为空，无法保存")
            return
        
        filepath = os.path.join(self.data_dir, filename)
        
        try:
            data.to_csv(filepath, index=False, encoding='utf-8-sig')
            logger.info(f"恒生指数PE数据已保存到: {filepath}")
            
            # 显示数据概览
            logger.info(f"数据概览:")
            logger.info(f"  总记录数: {len(data)}")
            logger.info(f"  日期范围: {data['日期'].min()} 到 {data['日期'].max()}")
            logger.info(f"  PE范围: {data['PE'].min():.2f} 到 {data['PE'].max():.2f}")
            
            # 显示最新PE
            latest_data = data.iloc[-1]
            logger.info(f"  最新PE: {latest_data['PE']:.2f} ({latest_data['日期'].strftime('%Y-%m-%d')})")
            
        except Exception as e:
            logger.error(f"保存数据时出错: {str(e)}")
    
    def plot_pe_trend(self, data, title='恒生指数市盈率(PE)历史走势'):
        """
        绘制PE历史走势图
        
        Args:
            data: pandas.DataFrame, PE数据
            title: str, 图表标题
        """
        if data is None or data.empty:
            logger.warning("数据为空，无法绘图")
            return
        
        try:
            # 创建图表
            plt.figure(figsize=(14, 8))
            
            # 绘制PE走势
            plt.plot(data['日期'], data['PE'], 'b-', linewidth=2, label='恒生指数PE')
            
            # 计算统计指标
            mean_pe = data['PE'].mean()
            std_pe = data['PE'].std()
            median_pe = data['PE'].median()
            
            # 添加统计线
            plt.axhline(y=mean_pe, color='r', linestyle='--', alpha=0.7, label=f'均值 ({mean_pe:.2f})')
            plt.axhline(y=median_pe, color='g', linestyle='--', alpha=0.7, label=f'中位数 ({median_pe:.2f})')
            
            # 添加±1个标准差线
            plt.axhline(y=mean_pe + std_pe, color='orange', linestyle=':', alpha=0.5, label=f'+1σ ({mean_pe + std_pe:.2f})')
            plt.axhline(y=mean_pe - std_pe, color='orange', linestyle=':', alpha=0.5, label=f'-1σ ({mean_pe - std_pe:.2f})')
            
            # 添加±2个标准差线
            plt.axhline(y=mean_pe + 2*std_pe, color='purple', linestyle=':', alpha=0.3, label=f'+2σ ({mean_pe + 2*std_pe:.2f})')
            plt.axhline(y=mean_pe - 2*std_pe, color='purple', linestyle=':', alpha=0.3, label=f'-2σ ({mean_pe - 2*std_pe:.2f})')
            
            # 设置图表标题和标签
            plt.title(title, fontsize=16, fontweight='bold')
            plt.xlabel('日期', fontsize=12)
            plt.ylabel('市盈率 (PE)', fontsize=12)
            
            # 添加图例
            plt.legend(loc='upper right')
            
            # 添加网格
            plt.grid(True, alpha=0.3)
            
            # 优化x轴日期显示
            plt.gcf().autofmt_xdate()
            
            # 添加当前PE值标注
            latest_pe = data['PE'].iloc[-1]
            latest_date = data['日期'].iloc[-1]
            plt.scatter(latest_date, latest_pe, color='red', s=100, zorder=5)
            plt.annotate(f'当前PE: {latest_pe:.2f}', 
                        xy=(latest_date, latest_pe),
                        xytext=(20, 20),
                        textcoords='offset points',
                        fontsize=12,
                        bbox=dict(boxstyle='round,pad=0.5', fc='yellow', alpha=0.8),
                        arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=0'))
            
            # 计算当前PE的百分位
            percentile = (data['PE'] <= latest_pe).mean() * 100
            
            # 添加百分位信息
            plt.text(0.02, 0.98, f'当前PE百分位: {percentile:.1f}%', 
                    transform=plt.gca().transAxes, 
                    fontsize=12,
                    bbox=dict(boxstyle='round,pad=0.5', fc='lightblue', alpha=0.8),
                    verticalalignment='top')
            
            # 保存图表
            image_path = os.path.join(self.image_dir, 'hsi_pe_eniu_trend.png')
            plt.tight_layout()
            plt.savefig(image_path, dpi=300, bbox_inches='tight')
            logger.info(f"PE走势图已保存到: {image_path}")
            
            plt.close()
            
        except Exception as e:
            logger.error(f"绘制PE走势图时出错: {str(e)}")
    
    def analyze_pe_valuation(self, data):
        """
        分析PE估值水平
        
        Args:
            data: pandas.DataFrame, PE数据
        """
        if data is None or data.empty:
            logger.warning("数据为空，无法分析")
            return
        
        try:
            latest_pe = data['PE'].iloc[-1]
            latest_date = data['日期'].iloc[-1]
            
            # 计算统计指标
            mean_pe = data['PE'].mean()
            std_pe = data['PE'].std()
            median_pe = data['PE'].median()
            min_pe = data['PE'].min()
            max_pe = data['PE'].max()
            
            # 计算百分位
            percentile = (data['PE'] <= latest_pe).mean() * 100
            
            # 计算Z分数
            z_score = (latest_pe - mean_pe) / std_pe
            
            print("\n" + "=" * 60)
            print("恒生指数PE估值分析")
            print("=" * 60)
            print(f"数据来源: eniu.com")
            print(f"数据期间: {data['日期'].min().strftime('%Y-%m-%d')} 至 {latest_date.strftime('%Y-%m-%d')}")
            print(f"总数据点: {len(data)} 个")
            
            print(f"\n📊 当前PE水平:")
            print(f"当前PE: {latest_pe:.2f} ({latest_date.strftime('%Y-%m-%d')})")
            print(f"历史百分位: {percentile:.1f}%")
            
            print(f"\n📈 历史统计:")
            print(f"历史均值: {mean_pe:.2f}")
            print(f"历史中位数: {median_pe:.2f}")
            print(f"标准差: {std_pe:.2f}")
            print(f"最小值: {min_pe:.2f}")
            print(f"最大值: {max_pe:.2f}")
            print(f"Z分数: {z_score:.2f}")
            
            print(f"\n🎯 估值判断:")
            if percentile <= 10:
                valuation = "严重低估"
                emoji = "🟢🟢🟢"
                suggestion = "极佳买入机会"
            elif percentile <= 25:
                valuation = "低估"
                emoji = "🟢🟢"
                suggestion = "较好买入机会"
            elif percentile <= 40:
                valuation = "略低估"
                emoji = "🟢"
                suggestion = "可考虑买入"
            elif percentile <= 60:
                valuation = "合理"
                emoji = "🟡"
                suggestion = "持有观望"
            elif percentile <= 75:
                valuation = "略高估"
                emoji = "🟠"
                suggestion = "谨慎操作"
            elif percentile <= 90:
                valuation = "高估"
                emoji = "🔴"
                suggestion = "考虑减仓"
            else:
                valuation = "严重高估"
                emoji = "🔴🔴"
                suggestion = "建议避险"
            
            print(f"估值水平: {valuation} {emoji}")
            print(f"投资建议: {suggestion}")
            
            # 与历史均值比较
            vs_mean = ((latest_pe - mean_pe) / mean_pe) * 100
            print(f"\n📊 相对历史均值: {vs_mean:+.1f}%")
            
            if abs(z_score) < 0.5:
                print("当前PE接近历史均值")
            elif z_score > 2:
                print("当前PE显著高于历史均值")
            elif z_score < -2:
                print("当前PE显著低于历史均值")
            elif z_score > 1:
                print("当前PE高于历史均值")
            elif z_score < -1:
                print("当前PE低于历史均值")
            
        except Exception as e:
            logger.error(f"分析PE估值时出错: {str(e)}")

def main():
    """主函数"""
    print("=" * 60)
    print("恒生指数PE数据收集器 (eniu.com)")
    print("=" * 60)
    
    # 创建收集器实例
    collector = HSIPEEniuCollector()
    
    # 获取PE数据
    pe_data = collector.get_hsi_pe_data()
    
    if not pe_data.empty:
        print(f"\n✅ 数据收集完成！")
        print(f"共获取 {len(pe_data)} 条恒生指数PE数据")
        
        # 保存数据
        collector.save_pe_data(pe_data)
        
        # 绘制走势图
        collector.plot_pe_trend(pe_data)
        
        # 分析估值水平
        collector.analyze_pe_valuation(pe_data)
        
    else:
        print("\n❌ 数据收集失败")
        print("请检查网络连接或API接口是否可用")

if __name__ == "__main__":
    main()
