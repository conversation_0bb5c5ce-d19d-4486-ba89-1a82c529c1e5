#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于ERP的恒生指数择时策略分析
利用股权风险溢价(ERP)对恒生指数进行择时投资
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import os
from datetime import datetime
import logging

# 设置中文字体
try:
    plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei']
    plt.rcParams['axes.unicode_minus'] = False
except:
    pass

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class HSIERPTimingStrategy:
    def __init__(self):
        """初始化择时策略分析器"""
        self.data_dir = "data_files"
        self.image_dir = "images"
        os.makedirs(self.image_dir, exist_ok=True)
        
        # 策略参数
        self.strategies = {
            "百分位策略": {
                "buy_threshold": 70,    # ERP百分位>70%时买入
                "sell_threshold": 30,   # ERP百分位<30%时卖出
                "description": "ERP百分位高于70%买入，低于30%卖出"
            },
            "标准差策略": {
                "buy_threshold": 1.0,   # ERP > 均值+1σ时买入
                "sell_threshold": -0.5, # ERP < 均值-0.5σ时卖出
                "description": "ERP高于均值+1σ买入，低于均值-0.5σ卖出"
            },
            "绝对值策略": {
                "buy_threshold": 6.0,   # ERP > 6%时买入
                "sell_threshold": 3.5,  # ERP < 3.5%时卖出
                "description": "ERP高于6%买入，低于3.5%卖出"
            },
            "动态策略": {
                "lookback_period": 252, # 回看期1年
                "buy_percentile": 75,   # 动态75%分位买入
                "sell_percentile": 25,  # 动态25%分位卖出
                "description": "基于过去1年动态分位数择时"
            }
        }
    
    def load_erp_data(self):
        """加载ERP数据"""
        try:
            erp_file = os.path.join(self.data_dir, "hsi_erp_data.csv")
            if not os.path.exists(erp_file):
                logger.error(f"ERP数据文件不存在: {erp_file}")
                return None
            
            data = pd.read_csv(erp_file)
            data['日期'] = pd.to_datetime(data['日期'])
            data = data.sort_values('日期').reset_index(drop=True)
            
            logger.info(f"成功加载ERP数据: {len(data)} 条记录")
            logger.info(f"日期范围: {data['日期'].min()} 到 {data['日期'].max()}")
            
            return data
            
        except Exception as e:
            logger.error(f"加载ERP数据时出错: {str(e)}")
            return None
    
    def calculate_signals(self, data):
        """计算各种择时信号"""
        try:
            logger.info("开始计算择时信号...")
            
            result_data = data.copy()
            
            # 计算统计指标
            erp_mean = data['ERP_百分比'].mean()
            erp_std = data['ERP_百分比'].std()
            
            # 计算历史百分位
            result_data['ERP_历史百分位'] = result_data['ERP_百分比'].rank(pct=True) * 100
            
            # 计算Z分数
            result_data['ERP_Z分数'] = (result_data['ERP_百分比'] - erp_mean) / erp_std
            
            # 1. 百分位策略信号
            strategy = self.strategies["百分位策略"]
            result_data['百分位_买入信号'] = result_data['ERP_历史百分位'] > strategy["buy_threshold"]
            result_data['百分位_卖出信号'] = result_data['ERP_历史百分位'] < strategy["sell_threshold"]
            
            # 2. 标准差策略信号
            strategy = self.strategies["标准差策略"]
            result_data['标准差_买入信号'] = result_data['ERP_Z分数'] > strategy["buy_threshold"]
            result_data['标准差_卖出信号'] = result_data['ERP_Z分数'] < strategy["sell_threshold"]
            
            # 3. 绝对值策略信号
            strategy = self.strategies["绝对值策略"]
            result_data['绝对值_买入信号'] = result_data['ERP_百分比'] > strategy["buy_threshold"]
            result_data['绝对值_卖出信号'] = result_data['ERP_百分比'] < strategy["sell_threshold"]
            
            # 4. 动态策略信号
            strategy = self.strategies["动态策略"]
            lookback = strategy["lookback_period"]
            
            result_data['动态_ERP_75分位'] = result_data['ERP_百分比'].rolling(window=lookback, min_periods=lookback//2).quantile(0.75)
            result_data['动态_ERP_25分位'] = result_data['ERP_百分比'].rolling(window=lookback, min_periods=lookback//2).quantile(0.25)
            result_data['动态_买入信号'] = result_data['ERP_百分比'] > result_data['动态_ERP_75分位']
            result_data['动态_卖出信号'] = result_data['ERP_百分比'] < result_data['动态_ERP_25分位']
            
            logger.info("择时信号计算完成")
            return result_data
            
        except Exception as e:
            logger.error(f"计算择时信号时出错: {str(e)}")
            return None
    
    def backtest_strategy(self, data, strategy_name):
        """回测单个策略"""
        try:
            logger.info(f"开始回测策略: {strategy_name}")
            
            buy_signal_col = f"{strategy_name}_买入信号"
            sell_signal_col = f"{strategy_name}_卖出信号"
            
            if buy_signal_col not in data.columns or sell_signal_col not in data.columns:
                logger.error(f"策略信号列不存在: {buy_signal_col}, {sell_signal_col}")
                return None
            
            # 初始化
            positions = []  # 持仓状态：1=持股，0=持现金
            returns = []    # 策略收益率
            trades = []     # 交易记录
            
            current_position = 0  # 初始持现金
            entry_price = 0
            
            for i, row in data.iterrows():
                date = row['日期']
                price = row['收盘价']
                buy_signal = row[buy_signal_col]
                sell_signal = row[sell_signal_col]
                
                # 交易逻辑
                if current_position == 0 and buy_signal:  # 买入
                    current_position = 1
                    entry_price = price
                    trades.append({
                        '日期': date,
                        '操作': '买入',
                        '价格': price,
                        'ERP': row['ERP_百分比']
                    })
                elif current_position == 1 and sell_signal:  # 卖出
                    current_position = 0
                    exit_return = (price - entry_price) / entry_price
                    trades.append({
                        '日期': date,
                        '操作': '卖出',
                        '价格': price,
                        'ERP': row['ERP_百分比'],
                        '收益率': exit_return * 100
                    })
                
                positions.append(current_position)
                
                # 计算当日收益率
                if i == 0:
                    daily_return = 0
                else:
                    if current_position == 1:  # 持股
                        daily_return = (price - data.iloc[i-1]['收盘价']) / data.iloc[i-1]['收盘价']
                    else:  # 持现金
                        daily_return = 0
                
                returns.append(daily_return)
            
            # 添加到数据中
            data[f'{strategy_name}_持仓'] = positions
            data[f'{strategy_name}_收益率'] = returns
            
            # 计算累计收益
            data[f'{strategy_name}_累计收益'] = (1 + pd.Series(returns)).cumprod()
            
            # 计算基准收益（买入持有）
            if f'买入持有_累计收益' not in data.columns:
                data['买入持有_收益率'] = data['收盘价'].pct_change().fillna(0)
                data['买入持有_累计收益'] = (1 + data['买入持有_收益率']).cumprod()
            
            logger.info(f"策略 {strategy_name} 回测完成，共 {len(trades)} 笔交易")
            
            return {
                'data': data,
                'trades': trades,
                'strategy_name': strategy_name
            }
            
        except Exception as e:
            logger.error(f"回测策略 {strategy_name} 时出错: {str(e)}")
            return None
    
    def calculate_performance_metrics(self, data, strategy_name):
        """计算策略绩效指标"""
        try:
            strategy_returns = data[f'{strategy_name}_收益率']
            benchmark_returns = data['买入持有_收益率']
            
            # 基本指标
            total_return = data[f'{strategy_name}_累计收益'].iloc[-1] - 1
            benchmark_return = data['买入持有_累计收益'].iloc[-1] - 1
            
            # 年化收益率
            years = (data['日期'].iloc[-1] - data['日期'].iloc[0]).days / 365.25
            annual_return = (1 + total_return) ** (1/years) - 1
            benchmark_annual = (1 + benchmark_return) ** (1/years) - 1
            
            # 波动率
            volatility = strategy_returns.std() * np.sqrt(252)
            benchmark_vol = benchmark_returns.std() * np.sqrt(252)
            
            # 夏普比率（假设无风险利率为3%）
            risk_free_rate = 0.03
            sharpe_ratio = (annual_return - risk_free_rate) / volatility if volatility > 0 else 0
            benchmark_sharpe = (benchmark_annual - risk_free_rate) / benchmark_vol if benchmark_vol > 0 else 0
            
            # 最大回撤
            cumulative = data[f'{strategy_name}_累计收益']
            rolling_max = cumulative.expanding().max()
            drawdown = (cumulative - rolling_max) / rolling_max
            max_drawdown = drawdown.min()
            
            benchmark_cumulative = data['买入持有_累计收益']
            benchmark_rolling_max = benchmark_cumulative.expanding().max()
            benchmark_drawdown = (benchmark_cumulative - benchmark_rolling_max) / benchmark_rolling_max
            benchmark_max_drawdown = benchmark_drawdown.min()
            
            # 胜率（正收益交易占比）
            positive_returns = strategy_returns[strategy_returns > 0]
            win_rate = len(positive_returns) / len(strategy_returns[strategy_returns != 0]) if len(strategy_returns[strategy_returns != 0]) > 0 else 0
            
            return {
                '策略名称': strategy_name,
                '总收益率': total_return * 100,
                '年化收益率': annual_return * 100,
                '年化波动率': volatility * 100,
                '夏普比率': sharpe_ratio,
                '最大回撤': max_drawdown * 100,
                '胜率': win_rate * 100,
                '基准总收益率': benchmark_return * 100,
                '基准年化收益率': benchmark_annual * 100,
                '基准夏普比率': benchmark_sharpe,
                '基准最大回撤': benchmark_max_drawdown * 100,
                '超额收益': (annual_return - benchmark_annual) * 100
            }
            
        except Exception as e:
            logger.error(f"计算绩效指标时出错: {str(e)}")
            return None
    
    def plot_strategy_analysis(self, data, trades_dict, performance_dict):
        """绘制策略分析图表"""
        try:
            logger.info("开始绘制策略分析图表...")
            
            fig, axes = plt.subplots(2, 2, figsize=(16, 12))
            fig.suptitle('基于ERP的恒生指数择时策略分析', fontsize=16, fontweight='bold')
            
            # 1. ERP走势和信号
            ax1 = axes[0, 0]
            ax1.plot(data['日期'], data['ERP_百分比'], 'b-', linewidth=1, label='ERP')
            
            # 添加买卖信号
            for strategy_name in ['百分位', '标准差']:
                if f'{strategy_name}_买入信号' in data.columns:
                    buy_signals = data[data[f'{strategy_name}_买入信号']]
                    sell_signals = data[data[f'{strategy_name}_卖出信号']]
                    
                    if not buy_signals.empty:
                        ax1.scatter(buy_signals['日期'], buy_signals['ERP_百分比'], 
                                  color='red', marker='^', s=30, alpha=0.7, label=f'{strategy_name}买入')
                    if not sell_signals.empty:
                        ax1.scatter(sell_signals['日期'], sell_signals['ERP_百分比'], 
                                  color='green', marker='v', s=30, alpha=0.7, label=f'{strategy_name}卖出')
            
            ax1.set_title('ERP走势与择时信号')
            ax1.set_xlabel('日期')
            ax1.set_ylabel('ERP (%)')
            ax1.legend()
            ax1.grid(True, alpha=0.3)
            
            # 2. 累计收益对比
            ax2 = axes[0, 1]
            ax2.plot(data['日期'], (data['买入持有_累计收益'] - 1) * 100, 
                    'k-', linewidth=2, label='买入持有', alpha=0.8)
            
            colors = ['red', 'blue', 'green', 'orange']
            for i, strategy_name in enumerate(['百分位', '标准差', '绝对值', '动态']):
                if f'{strategy_name}_累计收益' in data.columns:
                    ax2.plot(data['日期'], (data[f'{strategy_name}_累计收益'] - 1) * 100,
                            color=colors[i], linewidth=1.5, label=f'{strategy_name}策略')
            
            ax2.set_title('累计收益率对比')
            ax2.set_xlabel('日期')
            ax2.set_ylabel('累计收益率 (%)')
            ax2.legend()
            ax2.grid(True, alpha=0.3)
            
            # 3. 绩效指标对比
            ax3 = axes[1, 0]
            if performance_dict:
                strategies = list(performance_dict.keys())
                annual_returns = [performance_dict[s]['年化收益率'] for s in strategies]
                max_drawdowns = [abs(performance_dict[s]['最大回撤']) for s in strategies]
                
                x = np.arange(len(strategies))
                width = 0.35
                
                bars1 = ax3.bar(x - width/2, annual_returns, width, label='年化收益率', alpha=0.8)
                bars2 = ax3.bar(x + width/2, max_drawdowns, width, label='最大回撤', alpha=0.8)
                
                ax3.set_title('策略绩效对比')
                ax3.set_xlabel('策略')
                ax3.set_ylabel('百分比 (%)')
                ax3.set_xticks(x)
                ax3.set_xticklabels(strategies, rotation=45)
                ax3.legend()
                ax3.grid(True, alpha=0.3)
            
            # 4. 夏普比率和胜率
            ax4 = axes[1, 1]
            if performance_dict:
                sharpe_ratios = [performance_dict[s]['夏普比率'] for s in strategies]
                win_rates = [performance_dict[s]['胜率'] for s in strategies]
                
                ax4_twin = ax4.twinx()
                
                bars1 = ax4.bar(x - width/2, sharpe_ratios, width, label='夏普比率', alpha=0.8, color='blue')
                bars2 = ax4_twin.bar(x + width/2, win_rates, width, label='胜率', alpha=0.8, color='red')
                
                ax4.set_title('夏普比率与胜率')
                ax4.set_xlabel('策略')
                ax4.set_ylabel('夏普比率', color='blue')
                ax4_twin.set_ylabel('胜率 (%)', color='red')
                ax4.set_xticks(x)
                ax4.set_xticklabels(strategies, rotation=45)
                ax4.grid(True, alpha=0.3)
            
            plt.tight_layout()
            
            # 保存图表
            image_path = os.path.join(self.image_dir, 'hsi_erp_timing_strategy.png')
            plt.savefig(image_path, dpi=300, bbox_inches='tight')
            logger.info(f"策略分析图表已保存到: {image_path}")
            
            plt.close()
            
        except Exception as e:
            logger.error(f"绘制策略分析图表时出错: {str(e)}")

def main():
    """主函数"""
    print("=" * 60)
    print("基于ERP的恒生指数择时策略分析")
    print("=" * 60)
    
    # 创建策略分析器
    analyzer = HSIERPTimingStrategy()
    
    # 加载数据
    data = analyzer.load_erp_data()
    if data is None:
        print("\n❌ 数据加载失败")
        return
    
    # 计算择时信号
    data_with_signals = analyzer.calculate_signals(data)
    if data_with_signals is None:
        print("\n❌ 信号计算失败")
        return
    
    print(f"\n✅ 数据准备完成！")
    print(f"分析期间: {data['日期'].min().strftime('%Y-%m-%d')} 到 {data['日期'].max().strftime('%Y-%m-%d')}")
    print(f"数据点数: {len(data)} 个")
    
    # 回测各策略
    all_results = {}
    performance_metrics = {}
    
    strategy_names = ['百分位', '标准差', '绝对值', '动态']
    
    for strategy_name in strategy_names:
        print(f"\n📊 回测策略: {strategy_name}")
        
        result = analyzer.backtest_strategy(data_with_signals, strategy_name)
        if result:
            all_results[strategy_name] = result
            
            # 计算绩效指标
            metrics = analyzer.calculate_performance_metrics(result['data'], strategy_name)
            if metrics:
                performance_metrics[strategy_name] = metrics
                
                print(f"  总收益率: {metrics['总收益率']:.2f}%")
                print(f"  年化收益率: {metrics['年化收益率']:.2f}%")
                print(f"  最大回撤: {metrics['最大回撤']:.2f}%")
                print(f"  夏普比率: {metrics['夏普比率']:.2f}")
                print(f"  交易次数: {len(result['trades'])} 笔")
    
    # 显示绩效对比
    if performance_metrics:
        print("\n" + "=" * 60)
        print("策略绩效对比")
        print("=" * 60)
        
        # 基准表现
        if '百分位' in performance_metrics:
            benchmark_metrics = performance_metrics['百分位']
            print(f"买入持有基准:")
            print(f"  年化收益率: {benchmark_metrics['基准年化收益率']:.2f}%")
            print(f"  最大回撤: {benchmark_metrics['基准最大回撤']:.2f}%")
            print(f"  夏普比率: {benchmark_metrics['基准夏普比率']:.2f}")
        
        print(f"\n策略表现排名（按年化收益率）:")
        sorted_strategies = sorted(performance_metrics.items(), 
                                 key=lambda x: x[1]['年化收益率'], reverse=True)
        
        for i, (name, metrics) in enumerate(sorted_strategies, 1):
            print(f"{i}. {name}策略:")
            print(f"   年化收益率: {metrics['年化收益率']:.2f}%")
            print(f"   超额收益: {metrics['超额收益']:.2f}%")
            print(f"   最大回撤: {metrics['最大回撤']:.2f}%")
            print(f"   夏普比率: {metrics['夏普比率']:.2f}")
            print(f"   胜率: {metrics['胜率']:.1f}%")
    
    # 绘制分析图表
    if all_results and performance_metrics:
        # 使用第一个策略的数据（包含所有策略结果）
        combined_data = list(all_results.values())[0]['data']
        trades_dict = {name: result['trades'] for name, result in all_results.items()}
        
        analyzer.plot_strategy_analysis(combined_data, trades_dict, performance_metrics)
    
    print(f"\n✅ 择时策略分析完成！")

if __name__ == "__main__":
    main()
