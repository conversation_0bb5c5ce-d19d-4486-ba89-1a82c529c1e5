#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
恒生指数PE数据收集器
使用akshare获取恒生指数的市盈率(PE)数据
"""

import akshare as ak
import pandas as pd
import time
from datetime import datetime, timedelta
import logging
import os
import matplotlib.pyplot as plt
import matplotlib as mpl

# 设置中文字体
try:
    plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
    plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号
except:
    pass

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class HSIPECollector:
    def __init__(self):
        """初始化恒生指数PE数据收集器"""
        self.data_dir = "data_files"
        self.image_dir = "images"
        os.makedirs(self.data_dir, exist_ok=True)
        os.makedirs(self.image_dir, exist_ok=True)
        
        # 指数代码映射
        self.index_mapping = {
            "恒生指数": {
                "code": "HSI",
                "name": "恒生指数",
                "en_name": "Hang Seng Index"
            },
            "国企指数": {
                "code": "HSCEI",
                "name": "国企指数",
                "en_name": "HSCEI"
            },
            "红筹指数": {
                "code": "HSCCI",
                "name": "红筹指数",
                "en_name": "HSCCI"
            }
        }
    
    def get_hsi_pe_akshare(self):
        """
        使用akshare获取恒生指数PE数据
        
        Returns:
            pandas.DataFrame: 恒生指数PE数据
        """
        try:
            logger.info("正在使用akshare获取恒生指数PE数据...")
            
            # 使用akshare获取恒生指数估值数据
            hsi_valuation = ak.index_hk_valuation_baidu()
            
            if hsi_valuation is not None and not hsi_valuation.empty:
                logger.info(f"获取到恒生指数估值数据，列名: {list(hsi_valuation.columns)}")
                
                # 数据清理和格式化
                result_data = hsi_valuation.copy()
                
                # 确保日期列是日期类型
                if '日期' in result_data.columns:
                    result_data['日期'] = pd.to_datetime(result_data['日期'])
                
                # 确保PE列是数值类型
                pe_columns = [col for col in result_data.columns if 'PE' in col or '市盈率' in col]
                if pe_columns:
                    for col in pe_columns:
                        result_data[col] = pd.to_numeric(result_data[col], errors='coerce')
                
                # 按日期排序
                result_data = result_data.sort_values('日期').reset_index(drop=True)
                
                logger.info(f"成功获取 {len(result_data)} 条恒生指数PE数据")
                return result_data
            else:
                logger.warning("未获取到恒生指数PE数据，尝试备用方法...")
                return self.get_hsi_pe_alternative()
                
        except Exception as e:
            logger.error(f"使用akshare获取恒生指数PE数据时出错: {str(e)}")
            logger.info("尝试备用方法...")
            return self.get_hsi_pe_alternative()
    
    def get_hsi_pe_alternative(self):
        """
        使用akshare的其他接口获取恒生指数PE数据
        """
        try:
            logger.info("使用akshare备用接口获取恒生指数PE数据...")
            
            # 尝试使用指数行情接口
            hsi_data = ak.index_stock_info()
            
            if hsi_data is not None and not hsi_data.empty:
                logger.info(f"获取到指数数据，列名: {list(hsi_data.columns)}")
                
                # 筛选恒生指数
                hsi_row = hsi_data[hsi_data['代码'].str.contains('HSI', na=False) | 
                                  hsi_data['名称'].str.contains('恒生', na=False)]
                
                if not hsi_row.empty:
                    logger.info(f"找到恒生指数数据: {hsi_row.iloc[0].to_dict()}")
                    
                    # 创建结果DataFrame
                    result_data = pd.DataFrame({
                        '日期': [datetime.now().date()],
                        '指数': ['恒生指数'],
                        '代码': [hsi_row.iloc[0]['代码']],
                        '最新价': [hsi_row.iloc[0]['最新价']],
                    })
                    
                    # 如果有PE数据，添加PE列
                    if '市盈率' in hsi_row.columns:
                        result_data['市盈率'] = hsi_row.iloc[0]['市盈率']
                    
                    logger.info(f"备用方法成功获取恒生指数数据")
                    return result_data
                else:
                    logger.warning("备用方法未找到恒生指数数据")
                    return pd.DataFrame()
            else:
                logger.warning("备用方法未获取到指数数据")
                return pd.DataFrame()
                
        except Exception as e:
            logger.error(f"备用方法获取恒生指数PE数据时出错: {str(e)}")
            return pd.DataFrame()
    
    def get_hsi_index_data(self):
        """
        获取恒生指数行情数据
        
        Returns:
            pandas.DataFrame: 恒生指数行情数据
        """
        try:
            logger.info("正在获取恒生指数行情数据...")
            
            # 使用akshare获取恒生指数行情数据
            hsi_data = ak.index_zh_stock_hist(symbol="HSI", period="daily")
            
            if hsi_data is not None and not hsi_data.empty:
                logger.info(f"获取到恒生指数行情数据，列名: {list(hsi_data.columns)}")
                
                # 数据清理和格式化
                result_data = hsi_data.copy()
                
                # 确保日期列是日期类型
                if '日期' in result_data.columns:
                    result_data['日期'] = pd.to_datetime(result_data['日期'])
                
                # 按日期排序
                result_data = result_data.sort_values('日期').reset_index(drop=True)
                
                logger.info(f"成功获取 {len(result_data)} 条恒生指数行情数据")
                return result_data
            else:
                logger.warning("未获取到恒生指数行情数据")
                return pd.DataFrame()
                
        except Exception as e:
            logger.error(f"获取恒生指数行情数据时出错: {str(e)}")
            return pd.DataFrame()
    
    def save_hsi_pe_data(self, data, filename="hsi_pe_data.csv"):
        """
        保存恒生指数PE数据到CSV文件
        
        Args:
            data: pandas.DataFrame, 恒生指数PE数据
            filename: str, 文件名
        """
        if data is None or data.empty:
            logger.warning("数据为空，无法保存")
            return
        
        filepath = os.path.join(self.data_dir, filename)
        
        try:
            data.to_csv(filepath, index=False, encoding='utf-8-sig')
            logger.info(f"恒生指数PE数据已保存到: {filepath}")
            
            # 显示数据概览
            logger.info(f"数据概览:")
            logger.info(f"  总记录数: {len(data)}")
            
            if '日期' in data.columns:
                logger.info(f"  日期范围: {data['日期'].min()} 到 {data['日期'].max()}")
            
            # 查找PE列
            pe_columns = [col for col in data.columns if 'PE' in col or '市盈率' in col]
            if pe_columns:
                for col in pe_columns:
                    logger.info(f"  {col}范围: {data[col].min():.2f} 到 {data[col].max():.2f}")
                    
                    # 显示最新PE
                    latest_data = data.iloc[-1]
                    logger.info(f"  最新{col}: {latest_data[col]:.2f} ({latest_data['日期'] if '日期' in latest_data else '当前'})")
            
        except Exception as e:
            logger.error(f"保存数据时出错: {str(e)}")
    
    def plot_hsi_pe(self, data, pe_column='市盈率', title='恒生指数市盈率(PE)历史走势'):
        """
        绘制恒生指数PE历史走势图
        
        Args:
            data: pandas.DataFrame, 恒生指数PE数据
            pe_column: str, PE列名
            title: str, 图表标题
        """
        if data is None or data.empty or pe_column not in data.columns or '日期' not in data.columns:
            logger.warning(f"数据不完整，无法绘图。列名: {list(data.columns) if data is not None else None}")
            return
        
        try:
            # 创建图表
            plt.figure(figsize=(12, 6))
            
            # 绘制PE走势
            plt.plot(data['日期'], data[pe_column], 'b-', linewidth=2)
            
            # 添加均值线
            mean_pe = data[pe_column].mean()
            plt.axhline(y=mean_pe, color='r', linestyle='--', alpha=0.7)
            
            # 添加±1个标准差线
            std_pe = data[pe_column].std()
            plt.axhline(y=mean_pe + std_pe, color='g', linestyle='--', alpha=0.5)
            plt.axhline(y=mean_pe - std_pe, color='g', linestyle='--', alpha=0.5)
            
            # 添加±2个标准差线
            plt.axhline(y=mean_pe + 2*std_pe, color='y', linestyle='--', alpha=0.5)
            plt.axhline(y=mean_pe - 2*std_pe, color='y', linestyle='--', alpha=0.5)
            
            # 设置图表标题和标签
            plt.title(title, fontsize=16)
            plt.xlabel('日期', fontsize=12)
            plt.ylabel(pe_column, fontsize=12)
            
            # 添加图例
            plt.legend([pe_column, f'均值 ({mean_pe:.2f})', 
                       f'±1 标准差 ({mean_pe-std_pe:.2f}, {mean_pe+std_pe:.2f})',
                       f'±2 标准差 ({mean_pe-2*std_pe:.2f}, {mean_pe+2*std_pe:.2f})'])
            
            # 添加网格
            plt.grid(True, alpha=0.3)
            
            # 优化x轴日期显示
            plt.gcf().autofmt_xdate()
            
            # 添加当前PE值标注
            latest_pe = data[pe_column].iloc[-1]
            latest_date = data['日期'].iloc[-1]
            plt.scatter(latest_date, latest_pe, color='red', s=50, zorder=5)
            plt.annotate(f'当前: {latest_pe:.2f}', 
                        xy=(latest_date, latest_pe),
                        xytext=(10, 10),
                        textcoords='offset points',
                        fontsize=12,
                        bbox=dict(boxstyle='round,pad=0.5', fc='yellow', alpha=0.7))
            
            # 保存图表
            image_path = os.path.join(self.image_dir, 'hsi_pe_history.png')
            plt.tight_layout()
            plt.savefig(image_path, dpi=300, bbox_inches='tight')
            logger.info(f"PE走势图已保存到: {image_path}")
            
            # 显示图表
            plt.close()
            
        except Exception as e:
            logger.error(f"绘制PE走势图时出错: {str(e)}")
    
    def get_and_save_hsi_pe(self):
        """
        获取并保存恒生指数PE数据
        
        Returns:
            pandas.DataFrame: 恒生指数PE数据
        """
        # 获取恒生指数PE数据
        pe_data = self.get_hsi_pe_akshare()
        
        if pe_data is not None and not pe_data.empty:
            # 保存数据
            self.save_hsi_pe_data(pe_data, "hsi_pe_data.csv")
            
            # 查找PE列
            pe_columns = [col for col in pe_data.columns if 'PE' in col or '市盈率' in col]
            if pe_columns:
                # 绘制PE走势图
                self.plot_hsi_pe(pe_data, pe_columns[0])
            
            return pe_data
        else:
            logger.error("未能获取到恒生指数PE数据")
            return pd.DataFrame()

def main():
    """主函数"""
    print("=" * 60)
    print("恒生指数PE数据收集器")
    print("=" * 60)
    
    # 创建收集器实例
    collector = HSIPECollector()
    
    # 获取并保存恒生指数PE数据
    pe_data = collector.get_and_save_hsi_pe()
    
    if not pe_data.empty:
        print("\n✅ 数据收集完成！")
        print(f"共获取 {len(pe_data)} 条恒生指数PE数据")
        
        # 显示结果摘要
        print("\n📊 恒生指数PE数据摘要:")
        print("-" * 40)
        
        # 查找PE列
        pe_columns = [col for col in pe_data.columns if 'PE' in col or '市盈率' in col]
        if pe_columns:
            latest_data = pe_data.iloc[-1]
            for col in pe_columns:
                print(f"{col}: {latest_data[col]:.2f} ({latest_data['日期'].strftime('%Y-%m-%d') if '日期' in latest_data else '当前'})")
                
                # 计算百分位
                percentile = (pe_data[col] <= latest_data[col]).mean() * 100
                print(f"历史百分位: {percentile:.2f}%")
                
                # 计算与历史均值的关系
                mean_pe = pe_data[col].mean()
                std_pe = pe_data[col].std()
                z_score = (latest_data[col] - mean_pe) / std_pe
                print(f"历史均值: {mean_pe:.2f}, 标准差: {std_pe:.2f}")
                print(f"当前PE与均值偏离: {z_score:.2f}个标准差")
                
                # 估值建议
                if percentile < 10:
                    print("估值状态: 严重低估 🟢🟢🟢")
                elif percentile < 30:
                    print("估值状态: 低估 🟢🟢")
                elif percentile < 50:
                    print("估值状态: 略低估 🟢")
                elif percentile < 70:
                    print("估值状态: 适中 🟡")
                elif percentile < 90:
                    print("估值状态: 略高估 🔴")
                else:
                    print("估值状态: 高估 🔴🔴")
    else:
        print("\n❌ 数据收集失败")

if __name__ == "__main__":
    main()
