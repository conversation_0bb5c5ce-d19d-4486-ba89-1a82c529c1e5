#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
恒生指数ERP（股权风险溢价）计算器
ERP = 1/PE - 0.7×十年期美债收益率 - 0.3×十年期中债收益率
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import os
from datetime import datetime
import logging

# 设置中文字体
try:
    plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei']  # 用来正常显示中文标签
    plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号
except:
    pass

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class HSIERPCalculator:
    def __init__(self):
        """初始化ERP计算器"""
        self.data_dir = "data_files"
        self.image_dir = "images"
        os.makedirs(self.image_dir, exist_ok=True)
        
        # 数据文件路径
        self.hsi_pe_file = os.path.join(self.data_dir, "hsi_pe_eniu.csv")
        self.us_bond_file = os.path.join(self.data_dir, "bond_yields_美债10年.csv")
        self.cn_bond_file = os.path.join(self.data_dir, "bond_yields_中债10年.csv")
    
    def load_data(self):
        """加载所有数据"""
        try:
            logger.info("开始加载数据...")
            
            # 加载恒生指数PE数据
            if os.path.exists(self.hsi_pe_file):
                hsi_pe = pd.read_csv(self.hsi_pe_file)
                hsi_pe['日期'] = pd.to_datetime(hsi_pe['日期'])
                logger.info(f"成功加载恒生指数PE数据: {len(hsi_pe)} 条记录")
                logger.info(f"PE数据日期范围: {hsi_pe['日期'].min()} 到 {hsi_pe['日期'].max()}")
            else:
                logger.error(f"恒生指数PE数据文件不存在: {self.hsi_pe_file}")
                return None, None, None
            
            # 加载美债收益率数据
            if os.path.exists(self.us_bond_file):
                us_bond = pd.read_csv(self.us_bond_file)
                us_bond['日期'] = pd.to_datetime(us_bond['日期'])
                us_bond = us_bond.rename(columns={'收益率': '美债收益率'})
                logger.info(f"成功加载美债收益率数据: {len(us_bond)} 条记录")
                logger.info(f"美债数据日期范围: {us_bond['日期'].min()} 到 {us_bond['日期'].max()}")
            else:
                logger.error(f"美债收益率数据文件不存在: {self.us_bond_file}")
                return None, None, None
            
            # 加载中债收益率数据
            if os.path.exists(self.cn_bond_file):
                cn_bond = pd.read_csv(self.cn_bond_file)
                cn_bond['日期'] = pd.to_datetime(cn_bond['日期'])
                cn_bond = cn_bond.rename(columns={'收益率': '中债收益率'})
                logger.info(f"成功加载中债收益率数据: {len(cn_bond)} 条记录")
                logger.info(f"中债数据日期范围: {cn_bond['日期'].min()} 到 {cn_bond['日期'].max()}")
            else:
                logger.error(f"中债收益率数据文件不存在: {self.cn_bond_file}")
                return None, None, None
            
            return hsi_pe, us_bond, cn_bond
            
        except Exception as e:
            logger.error(f"加载数据时出错: {str(e)}")
            return None, None, None
    
    def merge_data(self, hsi_pe, us_bond, cn_bond):
        """合并数据"""
        try:
            logger.info("开始合并数据...")
            
            # 以恒生指数PE数据为基准进行合并
            merged_data = hsi_pe.copy()
            
            # 合并美债数据
            merged_data = pd.merge(merged_data, us_bond[['日期', '美债收益率']], 
                                 on='日期', how='left')
            
            # 合并中债数据
            merged_data = pd.merge(merged_data, cn_bond[['日期', '中债收益率']], 
                                 on='日期', how='left')
            
            # 显示合并后的数据概况
            logger.info(f"合并后数据总数: {len(merged_data)}")
            logger.info(f"有效PE数据: {merged_data['PE'].notna().sum()}")
            logger.info(f"有效美债数据: {merged_data['美债收益率'].notna().sum()}")
            logger.info(f"有效中债数据: {merged_data['中债收益率'].notna().sum()}")
            
            # 计算完整数据（三个指标都有值）
            complete_data = merged_data.dropna(subset=['PE', '美债收益率', '中债收益率'])
            logger.info(f"完整数据（三个指标都有值）: {len(complete_data)} 条")
            
            if len(complete_data) > 0:
                logger.info(f"完整数据日期范围: {complete_data['日期'].min()} 到 {complete_data['日期'].max()}")
            
            return merged_data, complete_data
            
        except Exception as e:
            logger.error(f"合并数据时出错: {str(e)}")
            return None, None
    
    def calculate_erp(self, data):
        """计算ERP"""
        try:
            logger.info("开始计算ERP...")
            
            # 计算ERP = 1/PE - 0.7×美债收益率 - 0.3×中债收益率
            # 注意：收益率数据是百分比形式，需要除以100转换为小数
            data = data.copy()
            data['盈利收益率'] = 1 / data['PE']  # 盈利收益率 = 1/PE
            data['美债权重收益率'] = 0.7 * (data['美债收益率'] / 100)  # 0.7倍美债收益率
            data['中债权重收益率'] = 0.3 * (data['中债收益率'] / 100)  # 0.3倍中债收益率
            data['ERP'] = data['盈利收益率'] - data['美债权重收益率'] - data['中债权重收益率']
            
            # 转换为百分比显示
            data['ERP_百分比'] = data['ERP'] * 100
            
            logger.info(f"成功计算 {len(data)} 条ERP数据")
            logger.info(f"ERP范围: {data['ERP_百分比'].min():.2f}% 到 {data['ERP_百分比'].max():.2f}%")
            
            # 显示最新数据
            latest = data.iloc[-1]
            logger.info(f"最新数据 ({latest['日期'].strftime('%Y-%m-%d')}):")
            logger.info(f"  PE: {latest['PE']:.2f}")
            logger.info(f"  盈利收益率: {latest['盈利收益率']:.4f} ({latest['盈利收益率']*100:.2f}%)")
            logger.info(f"  美债收益率: {latest['美债收益率']:.2f}%")
            logger.info(f"  中债收益率: {latest['中债收益率']:.2f}%")
            logger.info(f"  ERP: {latest['ERP_百分比']:.2f}%")
            
            return data
            
        except Exception as e:
            logger.error(f"计算ERP时出错: {str(e)}")
            return None
    
    def save_erp_data(self, data, filename="hsi_erp_data.csv"):
        """保存ERP数据"""
        try:
            filepath = os.path.join(self.data_dir, filename)
            
            # 选择要保存的列
            save_columns = ['日期', 'PE', '收盘价', '美债收益率', '中债收益率', 
                          '盈利收益率', 'ERP', 'ERP_百分比']
            save_data = data[save_columns].copy()
            
            save_data.to_csv(filepath, index=False, encoding='utf-8-sig')
            logger.info(f"ERP数据已保存到: {filepath}")
            
        except Exception as e:
            logger.error(f"保存ERP数据时出错: {str(e)}")
    
    def plot_erp_analysis(self, data, years=5):
        """绘制ERP分析图表"""
        try:
            logger.info(f"开始绘制最近{years}年ERP分析图表...")

            # 筛选最近N年的数据
            cutoff_date = datetime.now() - pd.DateOffset(years=years)
            recent_data = data[data['日期'] >= cutoff_date].copy()

            if len(recent_data) == 0:
                logger.warning(f"没有最近{years}年的数据")
                return

            # 创建子图
            fig, axes = plt.subplots(2, 2, figsize=(16, 12))
            fig.suptitle(f'恒生指数ERP（股权风险溢价）分析 - 最近{years}年', fontsize=16, fontweight='bold')
            
            # 1. ERP历史走势（最近N年）
            ax1 = axes[0, 0]
            ax1.plot(recent_data['日期'], recent_data['ERP_百分比'], 'b-', linewidth=1.5, label='ERP')

            # 添加统计线（基于最近N年数据）
            mean_erp = recent_data['ERP_百分比'].mean()
            std_erp = recent_data['ERP_百分比'].std()
            ax1.axhline(y=mean_erp, color='r', linestyle='--', alpha=0.7, label=f'均值 ({mean_erp:.2f}%)')
            ax1.axhline(y=mean_erp + std_erp, color='g', linestyle=':', alpha=0.5, label=f'+1σ ({mean_erp + std_erp:.2f}%)')
            ax1.axhline(y=mean_erp - std_erp, color='g', linestyle=':', alpha=0.5, label=f'-1σ ({mean_erp - std_erp:.2f}%)')

            ax1.set_title(f'ERP走势 - 最近{years}年')
            ax1.set_xlabel('日期')
            ax1.set_ylabel('ERP (%)')
            ax1.legend()
            ax1.grid(True, alpha=0.3)
            
            # 2. 各组成部分对比（最近N年）
            ax2 = axes[0, 1]
            ax2.plot(recent_data['日期'], recent_data['盈利收益率'] * 100, 'b-', linewidth=1, label='盈利收益率 (1/PE)')
            ax2.plot(recent_data['日期'], recent_data['美债收益率'], 'r-', linewidth=1, label='美债收益率')
            ax2.plot(recent_data['日期'], recent_data['中债收益率'], 'g-', linewidth=1, label='中债收益率')

            ax2.set_title(f'ERP组成部分 - 最近{years}年')
            ax2.set_xlabel('日期')
            ax2.set_ylabel('收益率 (%)')
            ax2.legend()
            ax2.grid(True, alpha=0.3)
            
            # 3. ERP分布直方图（最近N年）
            ax3 = axes[1, 0]
            ax3.hist(recent_data['ERP_百分比'], bins=30, alpha=0.7, color='skyblue', edgecolor='black')
            ax3.axvline(x=mean_erp, color='r', linestyle='--', linewidth=2, label=f'均值 ({mean_erp:.2f}%)')

            # 标注当前ERP
            current_erp = recent_data['ERP_百分比'].iloc[-1]
            ax3.axvline(x=current_erp, color='orange', linestyle='-', linewidth=2, label=f'当前 ({current_erp:.2f}%)')

            ax3.set_title(f'ERP分布直方图 - 最近{years}年')
            ax3.set_xlabel('ERP (%)')
            ax3.set_ylabel('频次')
            ax3.legend()
            ax3.grid(True, alpha=0.3)
            
            # 4. 最近一年ERP走势
            ax4 = axes[1, 1]
            recent_1y_data = recent_data.tail(252)  # 最近一年的数据
            ax4.plot(recent_1y_data['日期'], recent_1y_data['ERP_百分比'], 'b-', linewidth=2, label='ERP')
            ax4.axhline(y=mean_erp, color='r', linestyle='--', alpha=0.7, label=f'{years}年均值 ({mean_erp:.2f}%)')

            # 标注最新值
            latest = recent_1y_data.iloc[-1]
            ax4.scatter(latest['日期'], latest['ERP_百分比'], color='red', s=100, zorder=5)
            ax4.annotate(f'{latest["ERP_百分比"]:.2f}%',
                        xy=(latest['日期'], latest['ERP_百分比']),
                        xytext=(10, 10), textcoords='offset points',
                        bbox=dict(boxstyle='round,pad=0.5', fc='yellow', alpha=0.8))

            ax4.set_title('最近一年ERP走势')
            ax4.set_xlabel('日期')
            ax4.set_ylabel('ERP (%)')
            ax4.legend()
            ax4.grid(True, alpha=0.3)
            
            # 调整布局
            plt.tight_layout()
            
            # 保存图表
            image_path = os.path.join(self.image_dir, f'hsi_erp_analysis_{years}years.png')
            plt.savefig(image_path, dpi=300, bbox_inches='tight')
            logger.info(f"ERP分析图表已保存到: {image_path}")
            
            plt.close()
            
        except Exception as e:
            logger.error(f"绘制ERP分析图表时出错: {str(e)}")
    
    def analyze_erp_valuation(self, data, years=5):
        """分析ERP估值水平"""
        try:
            logger.info(f"开始分析最近{years}年的ERP估值水平...")

            # 筛选最近N年的数据
            cutoff_date = datetime.now() - pd.DateOffset(years=years)
            recent_data = data[data['日期'] >= cutoff_date].copy()

            if len(recent_data) == 0:
                logger.warning(f"没有最近{years}年的数据")
                return

            logger.info(f"最近{years}年数据: {len(recent_data)} 条")
            logger.info(f"日期范围: {recent_data['日期'].min().strftime('%Y-%m-%d')} 到 {recent_data['日期'].max().strftime('%Y-%m-%d')}")

            current_erp = recent_data['ERP_百分比'].iloc[-1]
            current_date = recent_data['日期'].iloc[-1]
            
            # 计算统计指标（基于最近N年数据）
            mean_erp = recent_data['ERP_百分比'].mean()
            std_erp = recent_data['ERP_百分比'].std()
            median_erp = recent_data['ERP_百分比'].median()
            min_erp = recent_data['ERP_百分比'].min()
            max_erp = recent_data['ERP_百分比'].max()

            # 计算百分位（基于最近N年数据）
            percentile = (recent_data['ERP_百分比'] <= current_erp).mean() * 100

            # 计算Z分数（基于最近N年数据）
            z_score = (current_erp - mean_erp) / std_erp

            # 同时计算全历史数据的统计指标用于对比
            all_mean_erp = data['ERP_百分比'].mean()
            all_percentile = (data['ERP_百分比'] <= current_erp).mean() * 100

            print("\n" + "=" * 60)
            print(f"恒生指数ERP（股权风险溢价）估值分析 - 最近{years}年")
            print("=" * 60)
            print(f"计算公式: ERP = 1/PE - 0.7×美债收益率 - 0.3×中债收益率")
            print(f"分析期间: {recent_data['日期'].min().strftime('%Y-%m-%d')} 至 {current_date.strftime('%Y-%m-%d')}")
            print(f"分析数据点: {len(recent_data)} 个")
            print(f"全历史数据点: {len(data)} 个")
            
            print(f"\n📊 当前ERP水平:")
            print(f"当前ERP: {current_erp:.2f}% ({current_date.strftime('%Y-%m-%d')})")
            print(f"最近{years}年百分位: {percentile:.1f}%")
            print(f"全历史百分位: {all_percentile:.1f}%")

            print(f"\n📈 最近{years}年统计:")
            print(f"均值: {mean_erp:.2f}%")
            print(f"中位数: {median_erp:.2f}%")
            print(f"标准差: {std_erp:.2f}%")
            print(f"最小值: {min_erp:.2f}%")
            print(f"最大值: {max_erp:.2f}%")
            print(f"Z分数: {z_score:.2f}")

            print(f"\n📈 全历史对比:")
            print(f"全历史均值: {all_mean_erp:.2f}%")
            print(f"当前vs全历史均值: {current_erp - all_mean_erp:+.2f}%")
            
            print(f"\n🔍 当前组成部分:")
            latest = recent_data.iloc[-1]
            print(f"恒生指数PE: {latest['PE']:.2f}")
            print(f"盈利收益率 (1/PE): {latest['盈利收益率']*100:.2f}%")
            print(f"美债收益率: {latest['美债收益率']:.2f}%")
            print(f"中债收益率: {latest['中债收益率']:.2f}%")
            print(f"美债权重贡献: {latest['美债权重收益率']*100:.2f}% (0.7 × {latest['美债收益率']:.2f}%)")
            print(f"中债权重贡献: {latest['中债权重收益率']*100:.2f}% (0.3 × {latest['中债收益率']:.2f}%)")
            
            print(f"\n🎯 ERP估值判断:")
            if percentile >= 90:
                valuation = "极高"
                emoji = "🔴🔴🔴"
                suggestion = "股权风险溢价极高，股票相对债券非常有吸引力"
            elif percentile >= 75:
                valuation = "较高"
                emoji = "🔴🔴"
                suggestion = "股权风险溢价较高，股票相对债券有吸引力"
            elif percentile >= 60:
                valuation = "略高"
                emoji = "🔴"
                suggestion = "股权风险溢价略高，股票相对债券略有吸引力"
            elif percentile >= 40:
                valuation = "适中"
                emoji = "🟡"
                suggestion = "股权风险溢价适中，股债配置均衡"
            elif percentile >= 25:
                valuation = "略低"
                emoji = "🟢"
                suggestion = "股权风险溢价略低，债券相对股票略有吸引力"
            elif percentile >= 10:
                valuation = "较低"
                emoji = "🟢🟢"
                suggestion = "股权风险溢价较低，债券相对股票有吸引力"
            else:
                valuation = "极低"
                emoji = "🟢🟢🟢"
                suggestion = "股权风险溢价极低，债券相对股票非常有吸引力"
            
            print(f"ERP水平: {valuation} {emoji}")
            print(f"投资含义: {suggestion}")
            
            # 与最近N年均值比较
            vs_mean = current_erp - mean_erp
            print(f"\n📊 相对最近{years}年均值: {vs_mean:+.2f}%")
            
            if abs(z_score) < 0.5:
                print("当前ERP接近历史均值")
            elif z_score > 2:
                print("当前ERP显著高于历史均值，股票相对债券极具吸引力")
            elif z_score < -2:
                print("当前ERP显著低于历史均值，债券相对股票极具吸引力")
            elif z_score > 1:
                print("当前ERP高于历史均值，股票相对债券有吸引力")
            elif z_score < -1:
                print("当前ERP低于历史均值，债券相对股票有吸引力")
            
            print(f"\n💡 投资建议:")
            if current_erp > mean_erp + std_erp:
                print("ERP处于高位，建议增加股票配置，减少债券配置")
            elif current_erp < mean_erp - std_erp:
                print("ERP处于低位，建议增加债券配置，减少股票配置")
            else:
                print("ERP处于正常范围，建议保持均衡的股债配置")
            
        except Exception as e:
            logger.error(f"分析ERP估值水平时出错: {str(e)}")

def main():
    """主函数"""
    print("=" * 60)
    print("恒生指数ERP（股权风险溢价）计算器")
    print("=" * 60)
    
    # 创建计算器实例
    calculator = HSIERPCalculator()
    
    # 加载数据
    hsi_pe, us_bond, cn_bond = calculator.load_data()
    
    if hsi_pe is None or us_bond is None or cn_bond is None:
        print("\n❌ 数据加载失败，请确保以下文件存在:")
        print(f"  - {calculator.hsi_pe_file}")
        print(f"  - {calculator.us_bond_file}")
        print(f"  - {calculator.cn_bond_file}")
        return
    
    # 合并数据
    merged_data, complete_data = calculator.merge_data(hsi_pe, us_bond, cn_bond)
    
    if complete_data is None or len(complete_data) == 0:
        print("\n❌ 数据合并失败或没有完整的数据")
        return
    
    # 计算ERP
    erp_data = calculator.calculate_erp(complete_data)
    
    if erp_data is None:
        print("\n❌ ERP计算失败")
        return
    
    print(f"\n✅ ERP计算完成！")
    print(f"共计算 {len(erp_data)} 条ERP数据")
    
    # 保存数据
    calculator.save_erp_data(erp_data)
    
    # 绘制分析图表
    calculator.plot_erp_analysis(erp_data)
    
    # 分析估值水平
    calculator.analyze_erp_valuation(erp_data)

if __name__ == "__main__":
    main()
