#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PCR恒生指数日频阈值择时策略回测 - 简化版
使用最佳阈值组合：85%-15%分位
每日判断PCR信号，次日开盘调仓
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def load_and_prepare_data():
    """加载并准备数据"""
    print("📊 正在加载PCR数据...")
    
    # 加载PCR数据
    pcr_df = pd.read_csv('data/Put_Call_Ratio.csv')
    pcr_df['Date'] = pd.to_datetime(pcr_df['Date'], format='%d/%m/%Y')
    pcr_df.set_index('Date', inplace=True)
    pcr_df.sort_index(inplace=True)
    
    # 计算阈值（日频使用更敏感的70%-30%组合）
    high_threshold = pcr_df['Put/Call Ratio'].quantile(0.70)
    low_threshold = pcr_df['Put/Call Ratio'].quantile(0.30)
    
    print(f"✅ PCR数据加载成功，共{len(pcr_df)}条记录")
    print(f"📅 数据时间范围：{pcr_df.index[0].date()} 到 {pcr_df.index[-1].date()}")
    print(f"🎯 阈值设定：")
    print(f"   高阈值(70%分位): {high_threshold:.4f} - 超过此值买入")
    print(f"   低阈值(30%分位): {low_threshold:.4f} - 低于此值卖出")
    
    # 获取恒生指数数据
    print("📈 正在获取恒生指数数据...")
    try:
        import yfinance as yf
        start_date = (pcr_df.index[0] - timedelta(days=30)).strftime('%Y-%m-%d')
        end_date = (pcr_df.index[-1] + timedelta(days=30)).strftime('%Y-%m-%d')
        
        hsi = yf.Ticker("^HSI")
        hsi_df = hsi.history(start=start_date, end=end_date)
        hsi_df.index = pd.to_datetime(hsi_df.index).tz_localize(None)
        hsi_df = hsi_df[['Close']]
        
        print(f"✅ 恒生指数数据获取成功，共{len(hsi_df)}条记录")
        
    except Exception as e:
        print(f"❌ 获取恒生指数数据失败：{e}")
        return None, None, None
    
    # 合并数据
    print("🔧 正在合并和对齐数据...")
    combined = pd.merge(pcr_df[['Put/Call Ratio']], hsi_df[['Close']], 
                       left_index=True, right_index=True, how='inner')
    combined['HSI_Return'] = combined['Close'].pct_change()
    combined = combined.dropna()
    
    print(f"✅ 数据准备完成，共{len(combined)}条有效记录")
    print(f"📅 回测时间范围：{combined.index[0].date()} 到 {combined.index[-1].date()}")
    
    return combined, high_threshold, low_threshold

def generate_daily_signals(df, high_threshold, low_threshold):
    """生成日频交易信号"""
    print("📡 正在生成日频交易信号...")
    
    # 初始化信号列
    df['Signal'] = 0  # 当日信号
    df['Position'] = 0  # 次日持仓
    
    # 每日计算信号
    current_position = 0
    signal_changes = 0
    
    for i, (idx, row) in enumerate(df.iterrows()):
        pcr_value = row['Put/Call Ratio']
        
        # 基于阈值的反向信号
        if pcr_value >= high_threshold:  # PCR高，市场悲观，买入
            new_signal = 1
        elif pcr_value <= low_threshold:  # PCR低，市场乐观，卖出
            new_signal = 0
        else:  # 在阈值之间，保持当前状态
            new_signal = current_position
        
        df.loc[idx, 'Signal'] = new_signal
        
        # 次日开盘调仓（使用当日信号，次日生效）
        df.loc[idx, 'Position'] = current_position
        
        # 统计信号变化
        if new_signal != current_position:
            signal_changes += 1
        
        current_position = new_signal
    
    # 统计信号
    buy_days = (df['Signal'] == 1).sum()
    sell_days = (df['Signal'] == 0).sum()
    high_pcr_days = (df['Put/Call Ratio'] >= high_threshold).sum()
    low_pcr_days = (df['Put/Call Ratio'] <= low_threshold).sum()
    
    print(f"📊 信号统计：")
    print(f"   做多天数: {buy_days} 天 ({buy_days/len(df):.1%})")
    print(f"   空仓天数: {sell_days} 天 ({sell_days/len(df):.1%})")
    print(f"   信号变化次数: {signal_changes} 次")
    print(f"   PCR > {high_threshold:.4f} 的天数: {high_pcr_days} 天")
    print(f"   PCR < {low_threshold:.4f} 的天数: {low_pcr_days} 天")
    
    return df

def backtest_strategy(df):
    """回测策略"""
    print("🚀 正在进行策略回测...")
    
    # 计算策略收益
    df['Strategy_Return'] = df['Position'].shift(1) * df['HSI_Return']
    df['Benchmark_Return'] = df['HSI_Return']
    
    # 计算累计收益
    df['Strategy_Cumulative'] = (1 + df['Strategy_Return']).cumprod()
    df['Benchmark_Cumulative'] = (1 + df['Benchmark_Return']).cumprod()
    
    # 计算回撤
    df['Strategy_Peak'] = df['Strategy_Cumulative'].expanding().max()
    df['Strategy_Drawdown'] = (df['Strategy_Cumulative'] - df['Strategy_Peak']) / df['Strategy_Peak']
    
    df['Benchmark_Peak'] = df['Benchmark_Cumulative'].expanding().max()
    df['Benchmark_Drawdown'] = (df['Benchmark_Cumulative'] - df['Benchmark_Peak']) / df['Benchmark_Peak']
    
    return df

def calculate_metrics(df):
    """计算绩效指标"""
    strategy_returns = df['Strategy_Return'].dropna()
    benchmark_returns = df['Benchmark_Return'].dropna()
    
    # 年化收益率
    trading_days = len(strategy_returns)
    years = trading_days / 252
    
    strategy_total_return = df['Strategy_Cumulative'].iloc[-1] - 1
    benchmark_total_return = df['Benchmark_Cumulative'].iloc[-1] - 1
    
    strategy_annual_return = (1 + strategy_total_return) ** (1/years) - 1
    benchmark_annual_return = (1 + benchmark_total_return) ** (1/years) - 1
    
    # 波动率
    strategy_volatility = strategy_returns.std() * np.sqrt(252)
    benchmark_volatility = benchmark_returns.std() * np.sqrt(252)
    
    # 夏普比率
    strategy_sharpe = strategy_annual_return / strategy_volatility if strategy_volatility > 0 else 0
    benchmark_sharpe = benchmark_annual_return / benchmark_volatility if benchmark_volatility > 0 else 0
    
    # 最大回撤
    strategy_max_drawdown = df['Strategy_Drawdown'].min()
    benchmark_max_drawdown = df['Benchmark_Drawdown'].min()
    
    # 胜率
    win_rate = (strategy_returns > 0).mean()
    
    # 交易次数
    position_changes = (df['Position'] != df['Position'].shift(1)).sum()
    
    return {
        'strategy_total_return': strategy_total_return,
        'benchmark_total_return': benchmark_total_return,
        'strategy_annual_return': strategy_annual_return,
        'benchmark_annual_return': benchmark_annual_return,
        'strategy_volatility': strategy_volatility,
        'benchmark_volatility': benchmark_volatility,
        'strategy_sharpe': strategy_sharpe,
        'benchmark_sharpe': benchmark_sharpe,
        'strategy_max_drawdown': strategy_max_drawdown,
        'benchmark_max_drawdown': benchmark_max_drawdown,
        'win_rate': win_rate,
        'total_trades': position_changes,
        'trading_days': trading_days,
        'years': years
    }

def print_report(metrics, high_threshold, low_threshold):
    """打印绩效报告"""
    print("\n" + "="*60)
    print("📊 PCR恒生指数日频阈值择时策略回测报告")
    print("="*60)
    
    print(f"\n🎯 策略参数:")
    print(f"   高阈值(70%分位): {high_threshold:.4f}")
    print(f"   低阈值(30%分位): {low_threshold:.4f}")
    
    print(f"\n📈 交易天数: {metrics['trading_days']} 天 ({metrics['years']:.2f} 年)")
    print(f"🔄 交易次数: {metrics['total_trades']} 次")
    
    print(f"\n💰 收益表现:")
    print(f"   策略总收益率: {metrics['strategy_total_return']:.2%}")
    print(f"   基准总收益率: {metrics['benchmark_total_return']:.2%}")
    print(f"   超额收益率: {metrics['strategy_total_return'] - metrics['benchmark_total_return']:.2%}")
    
    print(f"\n📊 年化指标:")
    print(f"   策略年化收益率: {metrics['strategy_annual_return']:.2%}")
    print(f"   基准年化收益率: {metrics['benchmark_annual_return']:.2%}")
    print(f"   策略年化波动率: {metrics['strategy_volatility']:.2%}")
    print(f"   基准年化波动率: {metrics['benchmark_volatility']:.2%}")
    
    print(f"\n📈 风险调整收益:")
    print(f"   策略夏普比率: {metrics['strategy_sharpe']:.3f}")
    print(f"   基准夏普比率: {metrics['benchmark_sharpe']:.3f}")
    
    print(f"\n📉 风险指标:")
    print(f"   策略最大回撤: {metrics['strategy_max_drawdown']:.2%}")
    print(f"   基准最大回撤: {metrics['benchmark_max_drawdown']:.2%}")
    print(f"   策略胜率: {metrics['win_rate']:.2%}")

def plot_results(df, high_threshold, low_threshold):
    """绘制回测结果"""
    print("📊 正在生成回测图表...")
    
    fig, axes = plt.subplots(4, 1, figsize=(15, 16))
    fig.suptitle(f'PCR恒生指数日频阈值择时策略回测结果\n(高阈值:{high_threshold:.3f}, 低阈值:{low_threshold:.3f})', 
                fontsize=16, fontweight='bold')
    
    # 1. 累计收益曲线
    ax1 = axes[0]
    ax1.plot(df.index, (df['Strategy_Cumulative'] - 1) * 100, 
            label='PCR日频策略', linewidth=2, color='red')
    ax1.plot(df.index, (df['Benchmark_Cumulative'] - 1) * 100, 
            label='买入持有基准', linewidth=2, color='blue')
    ax1.set_title('累计收益率对比 (%)', fontsize=14, fontweight='bold')
    ax1.set_ylabel('累计收益率 (%)')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 2. 回撤曲线
    ax2 = axes[1]
    ax2.fill_between(df.index, df['Strategy_Drawdown'] * 100, 0, 
                    alpha=0.3, color='red', label='策略回撤')
    ax2.fill_between(df.index, df['Benchmark_Drawdown'] * 100, 0, 
                    alpha=0.3, color='blue', label='基准回撤')
    ax2.set_title('回撤曲线 (%)', fontsize=14, fontweight='bold')
    ax2.set_ylabel('回撤 (%)')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 3. PCR指标和阈值
    ax3 = axes[2]
    ax3.plot(df.index, df['Put/Call Ratio'], label='PCR', linewidth=1, color='gray', alpha=0.7)
    ax3.axhline(y=high_threshold, color='red', linestyle='--', linewidth=2, 
               label=f'高阈值 ({high_threshold:.3f})')
    ax3.axhline(y=low_threshold, color='green', linestyle='--', linewidth=2, 
               label=f'低阈值 ({low_threshold:.3f})')
    
    # 标记信号区域
    high_pcr_mask = df['Put/Call Ratio'] >= high_threshold
    low_pcr_mask = df['Put/Call Ratio'] <= low_threshold
    
    ax3.fill_between(df.index, df['Put/Call Ratio'], high_threshold, 
                    where=high_pcr_mask, alpha=0.3, color='green', label='买入区域')
    ax3.fill_between(df.index, df['Put/Call Ratio'], low_threshold, 
                    where=low_pcr_mask, alpha=0.3, color='red', label='卖出区域')
    
    ax3.set_title('PCR指标与阈值信号', fontsize=14, fontweight='bold')
    ax3.set_ylabel('PCR值')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    # 4. 持仓状态
    ax4 = axes[3]
    ax4.fill_between(df.index, df['Position'], 0, alpha=0.3, color='green', label='持仓状态')
    ax4.set_title('持仓状态 (1=做多, 0=空仓)', fontsize=14, fontweight='bold')
    ax4.set_ylabel('持仓')
    ax4.set_xlabel('日期')
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    ax4.set_ylim(-0.1, 1.1)
    
    # 格式化x轴日期
    for ax in axes:
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
        ax.xaxis.set_major_locator(mdates.MonthLocator(interval=3))
        plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
    
    plt.tight_layout()
    
    # 保存图表
    filename = f'pcr_hsi_daily_strategy_{datetime.now().strftime("%Y%m%d_%H%M%S")}.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    print(f"📊 图表已保存为: {filename}")
    
    plt.show()

def main():
    """主函数"""
    print("🚀 开始PCR恒生指数日频阈值择时策略回测")
    print("="*60)
    
    # 1. 加载和准备数据
    df, high_threshold, low_threshold = load_and_prepare_data()
    if df is None:
        return
    
    # 2. 生成交易信号
    df = generate_daily_signals(df, high_threshold, low_threshold)
    
    # 3. 回测策略
    df = backtest_strategy(df)
    
    # 4. 计算绩效指标
    metrics = calculate_metrics(df)
    
    # 5. 打印报告
    print_report(metrics, high_threshold, low_threshold)
    
    # 6. 绘制图表
    plot_results(df, high_threshold, low_threshold)
    
    print("\n✅ 回测完成！")

if __name__ == "__main__":
    main()
