#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查akshare中可用的香港指数相关接口
"""

import akshare as ak
import pandas as pd

def check_akshare_hk_interfaces():
    """检查akshare中的香港指数接口"""
    print("=" * 60)
    print("检查akshare香港指数相关接口")
    print("=" * 60)
    
    # 检查可用的接口
    interfaces = [
        ("index_stock_info", "指数基本信息"),
        ("stock_hk_index_daily_em", "香港指数日线数据"),
        ("index_zh_a_hist", "A股指数历史数据"),
        ("macro_hk_market_info", "香港市场信息"),
        ("stock_hk_valuation_baidu", "香港股票估值"),
        ("index_investing_global", "全球指数数据"),
        ("stock_hk_index_spot_em", "香港指数实时数据"),
    ]
    
    for func_name, description in interfaces:
        try:
            print(f"\n尝试接口: {func_name} ({description})")
            
            if hasattr(ak, func_name):
                func = getattr(ak, func_name)
                
                # 尝试调用函数
                if func_name == "stock_hk_index_daily_em":
                    data = func(symbol="HSI", period="daily")
                elif func_name == "index_zh_a_hist":
                    data = func(symbol="000001", period="daily")
                elif func_name == "stock_hk_valuation_baidu":
                    data = func(symbol="00700")
                elif func_name == "index_investing_global":
                    data = func(country="香港", period="每日", start_date="20240101", end_date="20240201")
                elif func_name == "stock_hk_index_spot_em":
                    data = func()
                else:
                    data = func()
                
                if data is not None and not data.empty:
                    print(f"✅ 成功获取数据，形状: {data.shape}")
                    print(f"列名: {list(data.columns)}")
                    print("前3行数据:")
                    print(data.head(3))
                else:
                    print("❌ 未获取到数据")
            else:
                print(f"❌ 接口不存在")
                
        except Exception as e:
            print(f"❌ 接口调用出错: {str(e)}")

def check_hsi_data():
    """专门检查恒生指数数据"""
    print("\n" + "=" * 60)
    print("专门检查恒生指数数据")
    print("=" * 60)
    
    # 尝试不同的恒生指数获取方法
    methods = [
        ("stock_hk_index_daily_em", lambda: ak.stock_hk_index_daily_em(symbol="HSI", period="daily")),
        ("stock_hk_index_spot_em", lambda: ak.stock_hk_index_spot_em()),
        ("index_investing_global", lambda: ak.index_investing_global(country="香港", period="每日", start_date="20240701", end_date="20240801")),
    ]
    
    for method_name, method_func in methods:
        try:
            print(f"\n尝试方法: {method_name}")
            data = method_func()
            
            if data is not None and not data.empty:
                print(f"✅ 成功获取数据，形状: {data.shape}")
                print(f"列名: {list(data.columns)}")
                
                # 查找恒生指数相关数据
                if '名称' in data.columns:
                    hsi_data = data[data['名称'].str.contains('恒生', na=False)]
                    if not hsi_data.empty:
                        print("找到恒生指数数据:")
                        print(hsi_data)
                
                if 'symbol' in data.columns:
                    hsi_data = data[data['symbol'].str.contains('HSI', na=False)]
                    if not hsi_data.empty:
                        print("找到HSI数据:")
                        print(hsi_data)
                
                print("前3行数据:")
                print(data.head(3))
            else:
                print("❌ 未获取到数据")
                
        except Exception as e:
            print(f"❌ 方法调用出错: {str(e)}")

def main():
    """主函数"""
    print("akshare香港指数接口检查工具")
    
    # 检查接口
    check_akshare_hk_interfaces()
    
    # 专门检查恒生指数
    check_hsi_data()

if __name__ == "__main__":
    main()
