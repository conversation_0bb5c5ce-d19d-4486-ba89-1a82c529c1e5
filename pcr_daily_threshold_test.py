#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PCR恒生指数日频阈值测试
测试不同阈值组合对日频策略的影响
"""

import pandas as pd
import numpy as np
from datetime import timedelta
import warnings
warnings.filterwarnings('ignore')

def load_data():
    """加载数据"""
    # 加载PCR数据
    pcr_df = pd.read_csv('data/Put_Call_Ratio.csv')
    pcr_df['Date'] = pd.to_datetime(pcr_df['Date'], format='%d/%m/%Y')
    pcr_df.set_index('Date', inplace=True)
    pcr_df.sort_index(inplace=True)
    
    # 获取恒生指数数据
    try:
        import yfinance as yf
        start_date = (pcr_df.index[0] - timedelta(days=30)).strftime('%Y-%m-%d')
        end_date = (pcr_df.index[-1] + timedelta(days=30)).strftime('%Y-%m-%d')
        
        hsi = yf.Ticker("^HSI")
        hsi_df = hsi.history(start=start_date, end=end_date)
        hsi_df.index = pd.to_datetime(hsi_df.index).tz_localize(None)
        hsi_df = hsi_df[['Close']]
        
    except Exception as e:
        print(f"❌ 获取恒生指数数据失败：{e}")
        return None, None
    
    # 合并数据
    combined = pd.merge(pcr_df[['Put/Call Ratio']], hsi_df[['Close']], 
                       left_index=True, right_index=True, how='inner')
    combined['HSI_Return'] = combined['Close'].pct_change()
    combined = combined.dropna()
    
    return combined, pcr_df

def test_daily_strategy(df, pcr_df, high_pct, low_pct):
    """测试日频策略"""
    # 计算阈值
    high_threshold = pcr_df['Put/Call Ratio'].quantile(high_pct/100)
    low_threshold = pcr_df['Put/Call Ratio'].quantile(low_pct/100)
    
    df_test = df.copy()
    
    # 生成信号
    df_test['Signal'] = 0
    df_test['Position'] = 0
    
    current_position = 0
    signal_changes = 0
    
    for idx, row in df_test.iterrows():
        pcr_value = row['Put/Call Ratio']
        
        # 基于阈值的反向信号
        if pcr_value >= high_threshold:  # PCR高，买入
            new_signal = 1
        elif pcr_value <= low_threshold:  # PCR低，卖出
            new_signal = 0
        else:  # 保持当前状态
            new_signal = current_position
        
        df_test.loc[idx, 'Signal'] = new_signal
        df_test.loc[idx, 'Position'] = current_position
        
        if new_signal != current_position:
            signal_changes += 1
        
        current_position = new_signal
    
    # 回测
    df_test['Strategy_Return'] = df_test['Position'].shift(1) * df_test['HSI_Return']
    df_test['Benchmark_Return'] = df_test['HSI_Return']
    
    df_test['Strategy_Cumulative'] = (1 + df_test['Strategy_Return']).cumprod()
    df_test['Benchmark_Cumulative'] = (1 + df_test['Benchmark_Return']).cumprod()
    
    # 计算回撤
    df_test['Strategy_Peak'] = df_test['Strategy_Cumulative'].expanding().max()
    df_test['Strategy_Drawdown'] = (df_test['Strategy_Cumulative'] - df_test['Strategy_Peak']) / df_test['Strategy_Peak']
    
    # 计算指标
    strategy_returns = df_test['Strategy_Return'].dropna()
    
    trading_days = len(strategy_returns)
    years = trading_days / 252
    
    strategy_total_return = df_test['Strategy_Cumulative'].iloc[-1] - 1
    benchmark_total_return = df_test['Benchmark_Cumulative'].iloc[-1] - 1
    
    strategy_annual_return = (1 + strategy_total_return) ** (1/years) - 1
    benchmark_annual_return = (1 + benchmark_total_return) ** (1/years) - 1
    
    strategy_volatility = strategy_returns.std() * np.sqrt(252)
    strategy_sharpe = strategy_annual_return / strategy_volatility if strategy_volatility > 0 else 0
    
    strategy_max_drawdown = df_test['Strategy_Drawdown'].min()
    win_rate = (strategy_returns > 0).mean()
    
    # 统计信号
    buy_days = (df_test['Signal'] == 1).sum()
    high_pcr_days = (df_test['Put/Call Ratio'] >= high_threshold).sum()
    low_pcr_days = (df_test['Put/Call Ratio'] <= low_threshold).sum()
    
    return {
        'high_pct': high_pct,
        'low_pct': low_pct,
        'high_threshold': high_threshold,
        'low_threshold': low_threshold,
        'total_return': strategy_total_return,
        'annual_return': strategy_annual_return,
        'benchmark_return': benchmark_total_return,
        'excess_return': strategy_total_return - benchmark_total_return,
        'volatility': strategy_volatility,
        'sharpe_ratio': strategy_sharpe,
        'max_drawdown': strategy_max_drawdown,
        'win_rate': win_rate,
        'signal_changes': signal_changes,
        'buy_days': buy_days,
        'buy_days_pct': buy_days / len(df_test),
        'high_pcr_days': high_pcr_days,
        'low_pcr_days': low_pcr_days,
        'high_pcr_pct': high_pcr_days / len(df_test),
        'low_pcr_pct': low_pcr_days / len(df_test)
    }

def main():
    """主函数"""
    print("🚀 开始PCR恒生指数日频阈值测试")
    print("="*60)
    
    # 加载数据
    df, pcr_df = load_data()
    if df is None:
        return
    
    print(f"✅ 数据加载成功，共{len(df)}条记录")
    print(f"📅 回测时间范围：{df.index[0].date()} 到 {df.index[-1].date()}")
    
    # 测试不同阈值组合
    threshold_combinations = [
        (85, 15),  # 原来的极端阈值
        (80, 20),  # 标准阈值
        (75, 25),  # 较宽阈值
        (70, 30),  # 更宽阈值
        (65, 35),  # 很宽阈值
        (60, 40),  # 非常宽阈值
    ]
    
    results = []
    
    print(f"\n📊 PCR数据分布:")
    for pct in [15, 20, 25, 30, 35, 40, 60, 65, 70, 75, 80, 85]:
        value = pcr_df['Put/Call Ratio'].quantile(pct/100)
        print(f"   {pct:2d}%分位: {value:.4f}")
    
    print(f"\n🔄 测试不同阈值组合:")
    print("-" * 60)
    
    for high_pct, low_pct in threshold_combinations:
        print(f"\n测试阈值: 高{high_pct}%-低{low_pct}%分位")
        
        result = test_daily_strategy(df, pcr_df, high_pct, low_pct)
        results.append(result)
        
        print(f"  阈值: 高{result['high_threshold']:.4f}, 低{result['low_threshold']:.4f}")
        print(f"  总收益: {result['total_return']:.2%}, 超额收益: {result['excess_return']:.2%}")
        print(f"  夏普比率: {result['sharpe_ratio']:.3f}, 最大回撤: {result['max_drawdown']:.2%}")
        print(f"  交易次数: {result['signal_changes']}, 做多时间: {result['buy_days_pct']:.1%}")
        print(f"  高PCR天数: {result['high_pcr_days']}({result['high_pcr_pct']:.1%}), 低PCR天数: {result['low_pcr_days']}({result['low_pcr_pct']:.1%})")
    
    # 总结对比
    print("\n" + "="*80)
    print("📊 日频阈值策略对比总结")
    print("="*80)
    
    results_df = pd.DataFrame(results)
    
    # 显示关键指标
    display_cols = ['high_pct', 'low_pct', 'high_threshold', 'low_threshold', 
                   'total_return', 'excess_return', 'sharpe_ratio', 'max_drawdown', 
                   'signal_changes', 'buy_days_pct', 'high_pcr_pct', 'low_pcr_pct']
    
    print(results_df[display_cols].round(4).to_string(index=False))
    
    # 找出最佳策略
    best_sharpe_idx = results_df['sharpe_ratio'].idxmax()
    best_return_idx = results_df['total_return'].idxmax()
    best_excess_idx = results_df['excess_return'].idxmax()
    
    print(f"\n🏆 最佳策略:")
    print(f"   最佳夏普比率: 高{results[best_sharpe_idx]['high_pct']}%-低{results[best_sharpe_idx]['low_pct']}% (夏普比率: {results[best_sharpe_idx]['sharpe_ratio']:.3f})")
    print(f"   最佳总收益率: 高{results[best_return_idx]['high_pct']}%-低{results[best_return_idx]['low_pct']}% (总收益: {results[best_return_idx]['total_return']:.2%})")
    print(f"   最佳超额收益: 高{results[best_excess_idx]['high_pct']}%-低{results[best_excess_idx]['low_pct']}% (超额收益: {results[best_excess_idx]['excess_return']:.2%})")
    
    # 分析阈值触发频率与收益的关系
    print(f"\n📈 阈值触发频率分析:")
    for i, result in enumerate(results):
        trigger_freq = result['high_pcr_pct'] + result['low_pcr_pct']
        print(f"   {result['high_pct']}-{result['low_pct']}%: 触发频率{trigger_freq:.1%}, 收益{result['total_return']:.2%}, 夏普{result['sharpe_ratio']:.3f}")

if __name__ == "__main__":
    main()
