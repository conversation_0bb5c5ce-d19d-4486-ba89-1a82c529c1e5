#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证债券收益率数据
检查美债和中债数据是否正确
"""

import pandas as pd
import akshare as ak
import yfinance as yf

def check_akshare_bond_data():
    """检查akshare债券数据接口"""
    print("=" * 60)
    print("检查akshare债券数据接口")
    print("=" * 60)
    
    try:
        # 获取债券数据
        bond_data = ak.bond_zh_us_rate()
        print(f"数据形状: {bond_data.shape}")
        print(f"列名: {list(bond_data.columns)}")
        print("\n最新5行数据:")
        print(bond_data.tail())
        
        # 检查美债和中债数据是否不同
        if '美国国债收益率10年' in bond_data.columns and '中国国债收益率10年' in bond_data.columns:
            us_latest = bond_data['美国国债收益率10年'].iloc[-1]
            cn_latest = bond_data['中国国债收益率10年'].iloc[-1]
            print(f"\n最新美债10年收益率: {us_latest}%")
            print(f"最新中债10年收益率: {cn_latest}%")
            
            if us_latest == cn_latest:
                print("⚠️  警告：美债和中债收益率相同，可能数据有问题")
            else:
                print("✅ 美债和中债收益率不同，数据正常")
        
    except Exception as e:
        print(f"获取akshare数据时出错: {e}")

def check_yfinance_data():
    """检查yfinance美债数据"""
    print("\n" + "=" * 60)
    print("检查yfinance美债数据")
    print("=" * 60)
    
    try:
        # 获取美债10年期收益率
        ticker = yf.Ticker("^TNX")
        hist_data = ticker.history(period="1mo")  # 最近1个月
        
        if not hist_data.empty:
            print(f"数据形状: {hist_data.shape}")
            print(f"列名: {list(hist_data.columns)}")
            print("\n最新5行数据:")
            print(hist_data.tail())
            
            latest_yield = hist_data['Close'].iloc[-1]
            print(f"\n最新美债10年收益率: {latest_yield:.4f}%")
        else:
            print("未获取到yfinance数据")
            
    except Exception as e:
        print(f"获取yfinance数据时出错: {e}")

def check_alternative_china_bond():
    """检查其他中债数据接口"""
    print("\n" + "=" * 60)
    print("检查其他中债数据接口")
    print("=" * 60)
    
    # 尝试不同的接口
    interfaces = [
        ("bond_china_yield", ak.bond_china_yield),
        ("macro_china_treasury_rate", ak.macro_china_treasury_rate),
    ]
    
    for name, func in interfaces:
        try:
            print(f"\n尝试接口: {name}")
            data = func()
            if data is not None and not data.empty:
                print(f"数据形状: {data.shape}")
                print(f"列名: {list(data.columns)}")
                print("前5行数据:")
                print(data.head())
            else:
                print("未获取到数据")
        except Exception as e:
            print(f"接口 {name} 出错: {e}")

def main():
    """主函数"""
    print("债券收益率数据验证工具")
    
    # 检查akshare数据
    check_akshare_bond_data()
    
    # 检查yfinance数据
    check_yfinance_data()
    
    # 检查其他中债接口
    check_alternative_china_bond()

if __name__ == "__main__":
    main()
