#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试红利低波指数数据获取
"""

import akshare as ak
import pandas as pd
from datetime import datetime

def test_dividend_lowvol_data():
    """测试红利低波指数数据获取"""
    print("🔍 测试红利低波指数数据获取...")

    # 1. 先测试创业板50确保基础接口正常
    print("\n1. 测试创业板50指数 (399673)...")
    try:
        cyb50_data = ak.index_zh_a_hist(
            symbol="399673",
            period="daily",
            start_date="20240101",
            end_date="20241231"
        )
        if not cyb50_data.empty:
            print(f"  ✅ 创业板50成功获取 {len(cyb50_data)} 条数据")
        else:
            print("  ❌ 创业板50未获取到数据")
    except Exception as e:
        print(f"  ❌ 创业板50获取失败: {e}")

    # 2. 测试不同的红利低波指数接口
    print("\n2. 测试红利低波指数的不同接口...")

    # 方法1: 尝试中证指数接口
    print("\n  方法1: 尝试中证指数接口...")
    try:
        # 尝试获取中证指数列表
        csi_index_list = ak.index_zh_a_list()
        print(f"  📋 获取到 {len(csi_index_list)} 个指数")

        # 搜索红利相关指数
        dividend_indices = csi_index_list[csi_index_list['指数名称'].str.contains('红利|低波', na=False)]
        if not dividend_indices.empty:
            print("  🔍 找到红利相关指数:")
            for _, row in dividend_indices.iterrows():
                print(f"    {row['指数代码']}: {row['指数名称']}")
        else:
            print("  ❌ 未找到红利相关指数")

    except Exception as e:
        print(f"  ❌ 中证指数接口失败: {e}")

    # 方法2: 尝试ETF数据作为代理
    print("\n  方法2: 尝试红利低波ETF数据...")
    try:
        # 红利低波ETF代码通常是515450
        etf_data = ak.fund_etf_hist_em(symbol="515450", period="daily", start_date="20240101", end_date="20241231")
        if not etf_data.empty:
            print(f"  ✅ 红利低波ETF(515450)成功获取 {len(etf_data)} 条数据")
            print(f"  📅 数据范围: {etf_data['日期'].iloc[0]} 至 {etf_data['日期'].iloc[-1]}")
            print(f"  📊 最新收盘价: {etf_data['收盘'].iloc[-1]}")
            return "515450", "ETF"
        else:
            print("  ❌ 红利低波ETF未获取到数据")
    except Exception as e:
        print(f"  ❌ 红利低波ETF获取失败: {e}")

    # 方法3: 尝试其他可能的红利指数代码
    print("\n  方法3: 尝试其他红利指数代码...")
    alternative_codes = [
        ("000922", "中证红利指数"),
        ("000015", "红利指数"),
        ("399324", "深证红利指数"),
        ("H30269", "红利低波指数(原代码)")
    ]

    for code, name in alternative_codes:
        try:
            print(f"    尝试 {name} ({code})...")
            data = ak.index_zh_a_hist(
                symbol=code,
                period="daily",
                start_date="20240101",
                end_date="20241231"
            )
            if not data.empty:
                print(f"    ✅ {name} 成功获取 {len(data)} 条数据")
                return code, "INDEX"
            else:
                print(f"    ❌ {name} 未获取到数据")
        except Exception as e:
            print(f"    ❌ {name} 获取失败: {e}")

    print("\n❌ 所有方法都失败了，建议使用中证红利指数(000922)作为代理")
    return "000922", "INDEX"

if __name__ == "__main__":
    best_code, data_type = test_dividend_lowvol_data()
    print(f"\n🎯 推荐使用: {best_code} (类型: {data_type})")
